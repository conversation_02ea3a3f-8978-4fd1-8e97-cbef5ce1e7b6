user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;
daemon off;
load_module modules/ngx_http_geoip2_module.so;

events {
    worker_connections 1024;
}

http {
    limit_conn_zone $binary_remote_addr zone=addr:100m;
    server_tokens off;
    real_ip_header X-Forwarded-For;
    set_real_ip_from ********/16;
    geoip2 /etc/GeoLite2-Country.mmdb {
        auto_reload 5m;

        $geoip2_metadata_country_build metadata build_epoch;
        $geoip2_data_country_code default=AE country iso_code;
        $geoip2_data_country_name country names en;
    }


    log_format  main  '[[$time_local]] - [[$http_x_forwarded_for]] - [[$remote_addr]] - [[$request]] - [[$status]] - [[$request_time]] - [[$bytes_sent]] - [[$body_bytes_sent]] - [[$request_length]] - [[$upstream_addr]] - [[$upstream_status]] - [[$upstream_response_time]] - [[$http_referer]] - [[$http_user_agent]] - [[$remote_user]]';

    log_format healthd '$msec"$uri"'
                '$status"$request_time"$upstream_response_time"'
                '$http_x_forwarded_for';


    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;
    client_max_body_size 5m;
    gzip                on;
    gzip_http_version   1.0;
    gzip_proxied        any;
    gzip_min_length     500;
    gzip_disable        "MSIE [1-6]\.";
    gzip_types          text/plain text/xml text/css
                        text/comma-separated-values
                        text/javascript
                        application/x-javascript
                        application/atom+xml;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;


    index   index.html index.htm;

    upstream ecom-frontend {
        server ecom-frontend-app.ecom-frontend.svc.cluster.local:3000;
    }

    upstream ecom-mweb-frontend {
        server ecom-mweb-frontend-app.ecom-mweb-frontend.svc.cluster.local:3000;
    }

    upstream atwork-frontend {
        server atwork-frontend-app.atwork-frontend.svc.cluster.local:3000;
    }

    upstream solutions-hub-frontend {
        server solutions-hub-frontend-app.solutions-hub-frontend.svc.cluster.local:3000;
    }

    upstream solutions-hub-mweb-frontend {
        server solutions-hub-mweb-frontend-app.solutions-hub-mweb-frontend.svc.cluster.local:3000;
    }

    resolver ******** valid=5s;

    server {
        listen       80 default_server;
        server_name  yougotagift.com;
        root         /ygag/nginx;
        server_tokens off;

        set $ecom_frontend_service "ecom-frontend";

        if ($http_user_agent ~* iphone) {
          set $ecom_frontend_service "ecom-mweb-frontend";
        }

        if ($http_user_agent ~* android) {
          set $ecom_frontend_service "ecom-mweb-frontend";
        }

          if ($request_uri ~* "^/shop/([a-z]{2}-[a-z]{2}/)?feedback/.*" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

        if ($request_uri ~* "(^/shop/([a-z]{2}-[a-z]{2}/)?gifts/greetings/.*)" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

        if ($request_uri ~* "(^/shop/([a-z]{2}-[a-z]{2}/)?gifts/open/.*)" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

         if ($request_uri ~* "(^/shop/([a-z]{2}-[a-z]{2}/)?customer/experience/.*)" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

        if ($request_uri ~* "(^/shop/([a-z]{2}-[a-z]{2}/)?gifts/add-to-wallet/.*)" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

        if ($request_uri ~* "(^/shop/([a-z]{2}-[a-z]{2}/)?app-download)" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

        if ($request_uri ~* "(^/shop/([a-z]{2}-[a-z]{2}/)?view-brand)" ) {
          set $ecom_frontend_service "ecom-frontend";
        }

        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
            set $year $1;
            set $month $2;
            set $day $3;
            set $hour $4;
        }

        access_log  /var/log/nginx/access.log  main;

        location ~ ^/(_next/static) {
           return 302 https://cdn.ecomv2.yougotagift.com$request_uri;
        }


        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
        add_header  Strict-Transport-Security "max-age=0" always;
        add_header 'Cache-Control' 'private';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Max-Age' '1200';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';

        # Load configuration files for the default server block.

        set $mobile "";
        set $mobile_type "";
        set $XIsIpad "";

        if ( $http_user_agent ~* "ipad|gt|sgh|shw|sch-|sph-|sc-|gt-|nexus 7|nexus 10") {
            set $mobile "false";
            set $mobile_type "Tablet";
            set $XIsIpad "true";
        }

        if ($http_user_agent ~* iphone) {
            set $mobile_type "iphone";
            set $mobile "true";
        }

        if ($http_user_agent ~* android) {
            set $mobile_type "android";
            set $mobile "true";
        }

        if ($http_user_agent ~* BB10) {
            set $mobile_type "bb10";
            set $mobile "true";
        }

        if ($http_user_agent ~* Lumia) {
            set $mobile_type "lumia";
            set $mobile "true";
        }

        if ($http_user_agent ~* BlackBerry) {
            set $mobile_type "blackBerry";
            set $mobile "true";
        }

        if ($http_user_agent ~* YouGotaGiftApp) {
            set $mobile_type "iphone";
            set $mobile "true";
        }

       location /nginx-health {
            return 200;
            break;
       }

       location /favicon.ico {
            return 302  https://cdn.yougotagift.com/static/img/favicon.ico;
        }

       location /robots.txt {
            alias templates/robots.txt;
       }

       set $yougotagift_legacy legacy.yougotagift.com;
       location / {
              proxy_set_header Host               $yougotagift_legacy;
              proxy_set_header X-Real-IP          $remote_addr;
              proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
              proxy_set_header X-Forwarded-Host   $yougotagift_legacy:443;
              proxy_set_header X-Forwarded-Server $yougotagift_legacy;
              proxy_set_header X-Forwarded-Port   443;
              proxy_set_header X-Forwarded-Proto  "https";
              proxy_set_header Mobile $mobile;
              proxy_set_header Mobile-Type $mobile_type;
              proxy_set_header X-Is-Ipad $XIsIpad;

              proxy_read_timeout 180;
              proxy_connect_timeout 180;
              proxy_set_header Country $geoip2_data_country_code;

              proxy_set_header Referer https://$yougotagift_legacy$request_uri;
              proxy_pass https://$yougotagift_legacy;
       }

       location = /business {
          return 302 https://yougotagift.com/business/;
       }
        
       location = / {
          return 302 https://yougotagift.com/shop/;
       }

       location /shop/ {
           # limit_req zone=one;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $host:443;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto "https";

            proxy_read_timeout 180;
            proxy_redirect off;
            #limit_conn addr 50;
            proxy_pass http://$ecom_frontend_service;
       }


       location /atwork {
            # limit_req zone=one;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $host:443;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto  "https";

            proxy_read_timeout 180;
            proxy_redirect off;
            #limit_conn addr 50;
            proxy_pass http://atwork-frontend;
       }

       location /solutions-hub {
            # limit_req zone=one;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $host:443;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto  "https";

            proxy_read_timeout 180;
            proxy_redirect off;
            #limit_conn addr 50;

            if ($http_user_agent ~* (android|iphone)) {
               proxy_pass http://solutions-hub-mweb-frontend;
               break;
            }
            proxy_pass http://solutions-hub-frontend;
       }

        set $ecomweb_stores_domain app.stores.ecom.yougotagift.com;

        location /sitemap.xml {
           # limit_req zone=one;
            proxy_set_header Host               $ecomweb_stores_domain;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $ecomweb_stores_domain:443;
            proxy_set_header X-Forwarded-Server $ecomweb_stores_domain;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto  "https";
            proxy_read_timeout 180;

            rewrite  (.*) /en/sitemap.xml break;
            proxy_pass https://$ecomweb_stores_domain;
        }

        location /apple-app-site-association {
            rewrite  (.*) /shop/.well-known/apple-app-site-association last;
        }

        location /.well-known/apple-app-site-association {
            rewrite  (.*) /shop/.well-known/production/apple-app-site-association last;
        }

        location /.well-known/assetlinks.json {
             rewrite  (.*) /shop/.well-known/production/assetlinks.json last;
        }
    }
}
