user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;
daemon off;
#server_tokens off;


events {
    worker_connections 1024;
}

http {
    limit_conn_zone $binary_remote_addr zone=addr:100m;
    server_tokens off;

    # geoip_country        /usr/share/GeoIP/GeoIP.dat;
    # geoip_city           /usr/share/GeoIP/GeoLiteCity.dat;

    log_format  main  '[[$time_local]] - [[$http_x_forwarded_for]] - [[$remote_addr]] - [[$request]] - [[$status]] - [[$request_time]] - [[$bytes_sent]] - [[$body_bytes_sent]] - [[$request_length]] - [[$upstream_addr]] - [[$upstream_status]] - [[$upstream_response_time]] - [[$http_referer]] - [[$http_user_agent]] - [[$remote_user]]';

    log_format healthd '$msec"$uri"'
                '$status"$request_time"$upstream_response_time"'
                '$http_x_forwarded_for';


    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;
    client_max_body_size 4m;
    gzip                on;
    gzip_http_version   1.0;
    gzip_proxied        any;
    gzip_min_length     500;
    gzip_disable        "MSIE [1-6]\.";
    gzip_types          text/plain text/xml text/css
                        text/comma-separated-values
                        text/javascript
                        application/x-javascript
                        application/atom+xml;
    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    include /etc/nginx/conf.d/*.conf;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";

    index   index.html index.htm;

    upstream ecom_frontend {
        server ecom-frontend-[JIRA_ID]-app:3000;
    }

    server {
        listen       80 default_server;
        server_name  ecom-frontend-[JIRA_ID].sit.yougotagift.co;
        root         /ygag/nginx;
        server_tokens off;


        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
            set $year $1;
            set $month $2;
            set $day $3;
            set $hour $4;
        }

        resolver ********* valid=5s;

        access_log  /var/log/nginx/access.log  main;

        location ~ ^/(_next/static) {
           return 302  https://ecom-frontend-2-cdn.sit.yougotagift.co$request_uri;
        }

        location /.well-known/assetlinks.json {
           # limit_req zone=one;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $host:443;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto "https";

            proxy_read_timeout 180;
            proxy_redirect off;
            #limit_conn addr 50;

            rewrite  (.*) /shop/.well-known/qa/assetlinks.json break;
            proxy_pass http://ecom_frontend;
        }
        
        set $ecomweb_stores_cmwb_ecomv2 ecomweb-stores-cmwb-ecomv2.sit.yougotagift.co;         

        location /sitemap.xml {
           # limit_req zone=one;
            proxy_set_header Host               $ecomweb_stores_cmwb_ecomv2;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $ecomweb_stores_cmwb_ecomv2:443;
            proxy_set_header X-Forwarded-Server $ecomweb_stores_cmwb_ecomv2;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto  "https";
            proxy_read_timeout 180;

            rewrite  (.*) /en/sitemap.xml break;
            proxy_pass https://$ecomweb_stores_cmwb_ecomv2;
        }

        location /.well-known/apple-app-site-association {
            rewrite  (.*) /shop/.well-known/qa/apple-app-site-association last;
        }

        location = / {
           return 301 https://ecom-frontend-[JIRA_ID].sit.yougotagift.co/shop/;
        }


        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
        # add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive";
       #add_header Content-Security-Policy "default-src 'self' yougotagift.com *.yougotagift.com ecom-frontend-[JIRA_ID].sit.yougotagift.co; ygag-ecom-frontend-qa-1-tf.s3.us-east-2.amazonaws.com";
        add_header 'Cache-Control' 'private';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Max-Age' '1200';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Headers' 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';

       location /nginx-health {
            return 200;
            break;
       }

       location /favicon.ico {
            return 301  https://scdn.yougotagift.com/static/img/favicon.ico;
        }

       location /robots.txt {
            alias templates/robots.txt;
       }

       location / {
           # limit_req zone=one;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $host:443;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto "https";

            proxy_read_timeout 180;
            proxy_redirect off;
            #limit_conn addr 50;
            proxy_pass http://ecom_frontend;
       }

    }

}
