ingress:
  apiVersion: 'networking.k8s.io/v1'

  namespace: 'ecom-frontend'
  name: 'ecom-frontend-ingress'

  annotations:
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/backend-protocol: 'HTTP'
    alb.ingress.kubernetes.io/certificate-arn: 'arn:aws:acm:ap-south-1:844294577846:certificate/6f5cbc7a-16f7-401c-8f14-7ff6d1a63e0c'
    alb.ingress.kubernetes.io/group.name: 'legacy'
    alb.ingress.kubernetes.io/healthcheck-path: '/health/'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: 'internet-facing'
    alb.ingress.kubernetes.io/tags: 'Name=ygag-production-legacy-alb-eks-tf, Platform=EKS, Environment=production'
    alb.ingress.kubernetes.io/wafv2-acl-arn: 'arn:aws:wafv2:ap-south-1:844294577846:regional/webacl/AWSRegionalWAF2/a691c813-d84e-4501-966b-5e2b45240118'
    alb.ingress.kubernetes.io/target-type: 'ip'
    alb.ingress.kubernetes.io/subnets: 'ygag-production-default-public-ap-south-1a-tf, ygag-production-default-public-ap-south-1b-tf, ygag-production-default-public-ap-south-1c-tf'
    alb.ingress.kubernetes.io/ssl-policy: 'ELBSecurityPolicy-2016-08'
    alb.ingress.kubernetes.io/load-balancer-attributes: 'access_logs.s3.enabled=true,access_logs.s3.bucket=[ALB_ACCESS_LOG_BUCKET],access_logs.s3.prefix=legacy-public,idle_timeout.timeout_seconds=180'
    kubernetes.io/ingress.class: 'alb'
    alb.ingress.kubernetes.io/actions.redirect-business-to-solutions-hub: |
      {"Type": "redirect", "RedirectConfig": { 
        "Protocol": "HTTPS", 
        "Port": "443", 
        "Host": "yougotagift.com", 
        "Path": "/solutions-hub/",
        "StatusCode": "HTTP_302"
      }}
    alb.ingress.kubernetes.io/actions.redirect-enquiry-to-solutions-hub: |
      {"Type": "redirect", "RedirectConfig": { 
        "Protocol": "HTTPS", 
        "Port": "443", 
        "Host": "yougotagift.com", 
        "Path": "/solutions-hub/en/",
        "Query": "inquire-now",
        "StatusCode": "HTTP_302"
      }}

  rules:
    - host: 'yougotagift.com'
      http:
        paths:
          - backend:
              service:
                name: 'redirect-business-to-solutions-hub'
                port:
                  name: use-annotation
            path: /business/
            pathType: ImplementationSpecific
          - backend:
              service:
                name: 'redirect-enquiry-to-solutions-hub'
                port:
                  name: use-annotation
            path: /business/make-an-enquiry/
            pathType: ImplementationSpecific
          - backend:
              service:
                name: 'business-haproxy'
                port:
                  number: 8080
            path: '/business/*'
            pathType: 'ImplementationSpecific'
          - backend:
              service:
                name: 'ecom-frontend-nginx'
                port:
                  number: 80
            path: '/*'
            pathType: 'ImplementationSpecific'
    - host: 'www.yougotagift.com'
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ecom-frontend-nginx
                port:
                  number: 80
