ingress:
  apiVersion: 'networking.k8s.io/v1'

  namespace: 'ecom-frontend'
  name: 'ecom-frontend-ingress'

  annotations:
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/backend-protocol: 'HTTP'
    alb.ingress.kubernetes.io/certificate-arn: '[CERTIFICATE_ARN]'
    alb.ingress.kubernetes.io/group.name: 'legacy'
    alb.ingress.kubernetes.io/healthcheck-path: '/health/'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: 'internet-facing'
    alb.ingress.kubernetes.io/tags: 'Name=ygag-sandbox-legacy-alb-eks-tf, Platform=EKS, Environment=sandbox'
    alb.ingress.kubernetes.io/wafv2-acl-arn: '[WAF_ARN]'
    alb.ingress.kubernetes.io/target-type: 'ip'
    alb.ingress.kubernetes.io/subnets: 'ygag-sandbox-default-public-ap-south-1a-tf, ygag-sandbox-default-public-ap-south-1b-tf, ygag-sandbox-default-public-ap-south-1c-tf'
    alb.ingress.kubernetes.io/ssl-policy: 'ELBSecurityPolicy-TLS13-1-2-Ext2-2021-06'
    alb.ingress.kubernetes.io/load-balancer-attributes: 'access_logs.s3.enabled=true,access_logs.s3.bucket=[ALB_ACCESS_LOG_BUCKET],access_logs.s3.prefix=legacy-public,idle_timeout.timeout_seconds=180'
    kubernetes.io/ingress.class: 'alb'
    alb.ingress.kubernetes.io/actions.redirect-business-to-solutions-hub: |
      {"Type": "redirect", "RedirectConfig": { 
        "Protocol": "HTTPS", 
        "Port": "443", 
        "Host": "sandbox.yougotagift.com", 
        "Path": "/solutions-hub/",
        "StatusCode": "HTTP_302"
      }}
    alb.ingress.kubernetes.io/actions.redirect-enquiry-to-solutions-hub: |
      {"Type": "redirect", "RedirectConfig": { 
        "Protocol": "HTTPS", 
        "Port": "443", 
        "Host": "sandbox.yougotagift.com", 
        "Path": "/solutions-hub/en/",
        "Query": "inquire-now",
        "StatusCode": "HTTP_302"
      }}

  rules:
    - host: '[DOMAIN]'
      http:
        paths:
          - backend:
              service:
                name: 'redirect-business-to-solutions-hub'
                port:
                  name: use-annotation
            path: /business/
            pathType: ImplementationSpecific
          - backend:
              service:
                name: 'redirect-enquiry-to-solutions-hub'
                port:
                  name: use-annotation
            path: /business/make-an-enquiry/
            pathType: ImplementationSpecific
          - backend:
              service:
                name: 'business-haproxy'
                port:
                  number: 8080
            path: '/business/*'
            pathType: 'ImplementationSpecific'
          - backend:
              service:
                name: 'ecom-frontend-nginx'
                port:
                  number: 80
            path: '/*'
            pathType: 'ImplementationSpecific'
