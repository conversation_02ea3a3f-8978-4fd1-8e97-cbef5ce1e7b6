{"appTitle": "Buy eGift Cards in {{store}} | Gift Cards & Gift Vouchers", "checkBalance": "Check Balance", "switchLanguage": "العربية", "faq": "FAQ", "blog": "Blog", "helpline": "Helpline", "downloadApp": "Download App", "or": "or", "sundaySaturday": "Sunday–Saturday", "selectStore": "Select Store", "buyEGiftCards": "Buy eGift Cards", "startAGroupGift": "Start a Group Gift", "buyGamingCards": "Buy Gaming Cards", "forBusiness": "For Business", "account": "Account", "signIn": "Sign in", "cart": "<PERSON><PERSON>", "hey": "Hey", "myWallet": "My Wallet", "myOrders": "My Orders", "myGroupGift": "My Group Gift", "viewInApp": "View in App", "happyCredits": "Happy Credits", "eGiftCards": "eGift Cards", "yourAccount": "Your Account", "profile": "Profile", "orders": "Orders", "payments": "Payments", "logOut": "Log out", "downloadAppHeader": "Download the YOUGotaGift App", "downloadAppLabel": "Scan the QR code to download the app", "downloadAppLabelWallet": "To access your Wallet, Gifts & More", "downloadAppPlaceholder": "e.g. +971 5XXXXXXXX", "yourInviteHasBeenSent": "Your Invite has been Sent", "checkYourSMSInbox": "Check your sms inbox", "scanQRCode": "Scan QR Code", "learnMore": "Learn more.", "searchPlaceHolder": "What are you planning to gift today?", "oneCardAllBrand": "One Card. All Brands.", "yggRegisteredText": "YOUGotaGift Happy Card®", "happyCardDescription": "The YOUGotaGift Happy Card is a super card, redeemable at all of our\n partner brands in the UAE (Online & In-store brands). It is the most flexible gift for any occasion.", "happyCard": "Happy Card", "redeemableAt": "Redeemable at", "500Brands": "{{count}} + <PERSON>s", "mostFlexible": "Most flexible", "perfectEGift": "Perfect eGift Card", "redeemable": "Redeemable", "onlineAndStore": "Online & In-store", "quickView": "Quick View", "readMore": "Read More", "getStarted": "Get Started", "rewardBusiness": "Reward Business", "companyDataContent": "YOUGotaGift.com is the leading Digital Gift Card Company in the Middle East.", "featuredIn": "Featured In", "seeMore": "See More", "enterEmail": "Enter your email", "newsBottomMainHeading": "Celebrate family and friends, reward employees and customers.", "newsBottomSubHeading": "Gift Cards? YOUGotaGift.", "greatChoice": "Yay! Great choice.", "redeemAt": "Redeem at", "redeemableIn": "Redeemable in", "redemtion": "Redemption", "validity": "Validity", "offers": "offers", "aboutThisGift": "About this Gift Card", "howToUse": "How to Use", "redemptionDetails": "Redemption Details", "youMayLike": "You may like", "pickThisCard": "Pick this eGift Card", "veryHappy": "Very Happy", "happy": "Happy", "notHappy": "Not Happy", "errorTitle": "Uh Oh!", "errorSubTitle": "This is not quite right. Don’t worry, let’s get you back on track!", "errorButtonTitle": "Go to Home", "404": "404 - Page not found", "403": "403 - You don't have enough permissions to access the page you requested.", "500": "500 - We apologize. Refresh the page or try again later. If the problem persist, please contact <NAME_EMAIL>", "contact": "contact", "availableIn": "Available in", "downloadTheApp": "Download our App to enjoy lots of exciting features", "pleaseWait": "Please wait.", "loading": "Loading ....", "newsMainTitle1": "Subscribe to Our", "newsMainTitle2": "Newsletter", "newsSubTitle": "For updates, offers and other cool things we might be up to", "subscribe": "Subscribe", "upcomingOccasion": "Upcoming Occasion", "minMax": "Min - <PERSON> /", "sortBy": "Sort by", "selectCategory": "Select a Category", "selectOccasion": "Select an Occasion", "lastViewed": "Last viewed", "recommended": "Recommended", "lastViewedCards": "Last Viewed eGift Cards", "recommendedCards": "Recommended eGift Cards", "brandName": "Brand Name", "feedbackTitle": "Can’t Find", "feedbackTitleQuestion": "What You’re Looking For?", "feedbackSubtitle": "Let us know and we’ll add it!", "send": "Send", "feedbackSuccess": "Yay! Thank you", "invalidBrandName": "Invalid brand name", "seeLess": "See Less", "mostPopular": "Most Popular", "recentlyAdded": "Recently Added", "names": "Names", "howToSend": "How to send a Gift Card?", "helloGuest": "Hello, Guest!", "filters": "Filters", "noBrandsFound": "Currently we do not have any brands under this category!", "backToAllBrands": "Back to All Brands", "back": "Back", "todaysOccasion": "Today's Occasion", "invalidEmailAddress": "Invalid Email address", "loginOrSignUp": "Login / Signup", "orContinueWith": "or Continue with", "countryRegion": "Country/Region", "mobileNumber": "Mobile Number", "continue": "Continue", "enterVerificationCode": "Enter Verification Code", "weHaveSentVerificationCode": "We have sent the verification code to your mobile number", "weHaveSentVerificationCodeToEmail": "We have sent the verification code to your email", "weHaveReSentVerificationCode": "We have resent the verification code to your mobile number", "weHaveReSentVerificationCodeToEmail": "We have resent the verification code to your email", "invalidOTP": "Invalid OTP", "expiredOTP": "OTP Expired", "addMobileEnterDetails": "Enter Details", "verify": "Verify", "invalidPhoneNumber": "Enter valid Phone number", "invalidCountrySelection": "Select Country", "incorrectUsernameOrPassword": "Incorrect username or password", "authError": "Authentication Error", "timeLeftToEnterTheCodeReceived": "Time left to enter the code", "didntReceiveTheOTP": "Didn’t receive the code", "sendAgain": "Send Again", "enterDetails": "Enter Your Details", "emailText": "Email", "mobileText": "Mobile", "passwordText": "Password", "forgotYourPassword": "Forgot your password?", "changePasswordQ": "Change password", "toChangeYourPasswordEnterTheDetailsBelow": "To change your password, enter your details below.", "submit": "Submit", "needHelpQ": "Need help?", "contactUsVia": "Contact us via", "emailUs": "emailUs", "byContinuingYouAgreeToThe": "By continuing, you agree to the", "termsOfUse": "Terms of Use", "andText": "and", "name": "Name", "birthdayOptional": "Birthday (Optional)", "pleaseEnterYourPersonalEmail": "Please enter your personal email", "createPassword": "Create Password", "chooseASecurePasswordWhich": "Choose a secure password which", "hasMinimum6Characters": "Has minimum 6 characters", "has1SpecialCharacter": "Has 1 special character", "has1Digit": "Has 1 digit", "agreeAndContinue": "Agree & Continue", "allBrandsSearchResultMessage": "Showing results for <span class='highlight-title'>\"{{CardName}}\"</span> - {{CardCount}} eGift Cards", "mobileAlreadyAssociatedWithAnotherAccount": "Mobile number is already associated with another account", "invalidEmailMessage": "Please enter a valid email address", "passwordRequired": "Please enter password", "searchedFor": "You searched for", "noMatchesFound": "We couldn’t find any matches!", "checkSpelling": "Please check the spelling or try searching for another Gift Card, Brand or Category.", "selectdesign": "Select Your Design", "selectCardValue": "Select Card Value", "setYourOwn": "or Set Your Own", "cardValueValidationMessage": "Invalid denomination for this eGift Card", "locations": "Locations", "brands": "Brands", "reviews": "Reviews", "skipStep": "Skip this Step", "orderTypeTitle": "How would you like to buy this eGift card?", "self": "Buy for Self", "gift": "Gift a Friend", "quantity": "Quantity", "downloadOurApp": "Download Our App", "change": "Change", "stockMessage": "Only {{stock}} left in stock", "outOfStock": "Out of stock!", "usePhoto": "Use Photo", "useVideo": "Use Video", "usePhotoDescription": "Personalize Photo with stickers, texts, audio or custom styling.", "useVideoDescription": "Capture a video to add the personal touch to the gift.", "personalize": "Personalize", "discard": "Discard", "sendto": "Send to...", "mobile": "Mobile (Optional)", "tip": "Tip: ", "sendviaemailorsms": "You can send via email or SMS or both!", "greetingtitle": "Add Greeting Cover", "yourMessage": "Your Message", "backGroundColor": "BG Color", "uploadingVideo": "Uploading...", "processingVideo": "Processing...", "scheduleTitle": "Schedule eGift Card Delivery", "instant": "Instant Delivery", "recomendedcards": "Recommended Cards", "aboutUs": "About Us", "companyDescription": "YOUGotaGift.com is the leading Digital Gift Card Company in the Middle East.", "aboutDigitalGiftingPlatform": "Our digital gifting platform is used by individuals and businesses to celebrate, reward, motivate and show appreciation to friends, loved ones, employees, customers and business partners.", "aboutBusinessSolution": "Our business solution has also been adopted by major customer loyalty programs across leading telcos, banks, airlines delivering unparalleled choice of rewards, a memorable customer service experience, and best-in-class technology tailored to partner needs.", "meetOurFounders": "Meet Our Founders", "HusainMakia": "<PERSON><PERSON><PERSON>", "CEO": "(CEO & Co-Founder)", "HusainMakiaQuote": "\"no pain no gain\"", "aboutHusainMakia": "Following an early career in business development with <PERSON><PERSON><PERSON>, a major US oil and gas contractor, <PERSON><PERSON><PERSON> co-founded Zawya, a leading business and finance information portal for the region. He managed its operations for over a decade and recently sold it to an international information leader Thomson Reuters. He is now a Partner in YOUGotaGift.com LTD, where he directs start-ups to succeed. He earned an MBA and a bachelor’s degree in Civil Engineering from Imperial College, London.", "AbedBibi": "Abed <PERSON>", "coFounder": "(Co-Founder)", "abedBibiQuote": "\"no marketing no fun\"", "aboutAbedBibi": "Over 20+ years of experience in the GCC, resident of the UAE, working in this region and is influential in persuading stakeholders. He was one of the founding partners of Wolff Olins in the UAE and India, a major international branding consultancy.", "aboutAbedBibi2": "Co-founded YOUGotaGift.com LTD after selling Wolff Olins shares to Omnicom group. Expert in the Marketing and Branding Industry in the Gulf and Levant, and based in Dubai. He spent many years at MBC Group as General Manager of the commercial company Arab Media Services. Prior to that, he was the co-founder of Future TV.", "aboutAbedBibi3": "Regional Director of the largest Media House - Al Khaleejiah Media- which is part of the publishing house and distribution,Saudi Research and Publishing Company.", "setDate": "Set Date", "addToCart": "Add To Cart", "animation": "Animation", "standard": "Standard", "english": "English", "arabic": "Arabic", "greetings": "Greetings", "personalization": "Personalization", "today": "Today", "tomorrow": "Tomorrow", "dayAfterTomorrow": "Day After Tomorrow", "custom": "Custom", "gmt": "GMT", "time": "Time", "successAdded": "Successfully Added", "successAddedMessage": "eGift Card added to your cart successfully", "charLimit": "Character Limit", "charLimitMessage": "Please restrict your message between 2 to 720 characters", "privacyPolicy": "Privacy Policy", "privacyPolicyIntro": "YOUGotaGift.com Ltd. (\"YOUGotaGift.com\") is committed towards securing the privacy of information of those who use our services. We wrote this policy to help you understand what information we collect, how we use it and what choices you have about it. We welcome your questions and comments on this policy.", "privacyPolicyPrecautions": "Please read this Privacy Policy closely. By visiting our website or using our mobile application and/or providing your personal information to YOUGotaGift.com, you agree that YOUGotaGift.com may collect, use, and disclose your personal information as described in this Privacy Policy. We routinely update this Privacy Policy as our products and offerings change, so check back regularly for updates or changes. By using YOUGotaGift.com web and/or mobile applications (together the “Applications”), you are accepting the practices described in this Privacy Policy.", "whatDoWeGatherQ": "How and What Personal Information Do We Gather?", "whatDoWeGatherDesc": "YOUGotaGift.com collects information when you use the Applications. For example, we collect data when you purchase or receive an eGift Card or Physical Gift Card (collectively “Gift Card”) or we collect data when you contact our Customer Support. We only collect as much information as we need for specific and identified purposes.", "personalInfo": "Personal Information :", "personalInfoDesc": " When you purchase or receive a Gift Card, you will be asked to provide certain personal information, such as your name, email address, phone number, or similar information. This information is collected to enable YOUGotaGift.com to process your order.", "personalInfosub1": "We do not store your credit card number, this information is directly passed to our payment gateway provider for the purpose of processing your payment.", "personalInfosub2": "We may also collect personally identifiable information automatically when you use the Applications with Facebook or Google. Some of this information we receive are needed to set up your account and send your gift. You can choose not to use Facebook or Google with the Applications, and you can also choose not to provide certain personally identifiable information, but then you might not be able to take advantage of the services provided by the Applications.", "cookiesInfo": "Cookies Information :", "cookiesInfoDesc": "When you visit the Applications, we may send one or more cookies (a small text file containing a string of alphanumeric characters) to your computer. YOUGotaGift.com uses both session cookies and persistent cookies. A persistent cookie remains after you close your browser and may be used by your browser on subsequent visits to the Sites. These persistent cookies can be removed, but each web browser is a little different. Please review your browser’s “Help” file to learn the correct way to modify your cookies setup. We do not link the information we store in cookies to any personally identifiable information you submit while on our site.", "autoCollectedInfo": "Automatically Collected Information : ", "autoCollectedInfoDesc": "When you use the Applications or open one of our emails, we automatically record certain data using technology, including “clear gifs” or “web beacons,” cookies (discussed above), IP addresses, and unique “device IDs” (similar to IP addresses). “Automatically Collected” data includes web browser type, the website that referred you to us, the web pages you viewed on the Applications, and the dates and times that you visited the Applications.", "clientProvidedInfo": "Client Provided Information : ", "clientProvidedInfoDesc": "When YOUGotaGift.com manages promotions or other services for a Client (a company or organization to which YOUGotaGift.com provides business services) some personal information is also shared with YOUGotaGift.com in order to process the Gift Card creation and redemption activity on behalf of that Client. The use of personal information shared with us by our clients is also subject to the conditions mentioned in this privacy policy.", "recieversOfGiftCards": "Receivers of Gift Cards", "recieversOfGiftCardsDesc": "When you choose to use our services to send a Gift Card to anyone, you represent that you have their consent to provide us their name, email address and/or mobile number. We will automatically send the receiver of Gift Card an email or mobile SMS informing them of the received Gift Card. Unless we are authorized by your friend, we will only use your friend’s name, email address and/or mobile number for the purposes of sending this email and reminder emails for using the Gift Cards and maintaining an activity log of it.", "useOfPersonalInfoQ": "How We Use the Personal Information?", "useOfPersonalInfop1": "We use personally identifiable information to respond to your requests, sell you a Gift Card, improve your purchasing experience, customize your future purchases, improve our Applications, improve our marketing and promotional efforts, and to communicate with you for order verification, customer service purposes, send you reminders for use of Gift Cards, notices, administrative messages, and requested information, including such messages on behalf of our Clients and occasionally send you marketing information about YOUGotaGift.com and our business partners and their products and services. We also use such information to troubleshoot problems, resolve disputes, accomplish administrative tasks, enforce our agreements with you, including our Terms and Conditions and this Privacy Policy, comply with applicable law and cooperate with law enforcement activities.", "useOfPersonalInfop2": "The processing and use of the data you provide to us is done in the United Arab Emirates and United States of America. By using the Applications and providing information to YOUGotaGift.com, you are expressly consenting to this.", "isInfoSecureQ": "Does YOUGotaGift.com Share/Disclose the Information It Receives?", "isInfoSecurep1": "YOUGotaGift.com does not share your information with any other party. We only use the information internally for our services and the Applications as described above. YOUGotaGift.com will not sell your personal information to third parties.", "isInfoSecurep2": "In certain cases, we may disclose customer information if required to do so by law or in the good-faith belief that such action is necessary to comply with applicable laws or respond to a court order, judicial or other government subpoena, or warrant.", "isInfoSecurep3": "We also reserve the right to disclose customer information that we believe, in good faith, is appropriate or necessary to take precautions to protect customers from fraudulent, abusive, or unlawful uses; to investigate and defend ourselves against any third-party claims or allegations; to assist government enforcement agencies; to protect the security or integrity of the Applications; and to protect against fraudulent uses, the rights, property, or personal safety of YOUGotaGift.com, our Clients, or others.", "howSecureQ": "How Secure Is your Information with YOUGotaGift.com?", "howSecureDesc": "YOUGotaGift.com is committed to protecting the Personal Data you share with us. We utilize a combination of industry-standard security technologies, procedures, and organizational measures to help protect your Personal Data from unauthorized access, use or disclosure.", "howSecurep1": "We work to protect the security of your information during transmission by using Secure Sockets Layer (SSL) software, which encrypts information you input.", "howSecurep2": "We reveal only the last four digits of your credit/debit card numbers when confirming an order. Of course, we transmit the entire credit/debit card number to the appropriate credit/debit card company during order processing. We do not store this information in our Applications.", "howSecurep3": "It is important for you to protect against unauthorized access to your password and to your computer. Be sure to sign off when finished using a shared computer.", "howSecurep4": "If you use the Applications with Facebook make sure you either log out from Facebook or through the Applications (which will also log you out of Facebook also) if you are using a shared computer.", "howSecureEnqueries": "If you have any questions about the security of your Personal Data, you can contact <NAME_EMAIL>.", "yourChoicesQ": "What Choices Do You Have?", "yourChoicesDesc": "Our goal is to give you simple and meaningful choices regarding your information. If you have a YOUGotaGift.com account, many of the choices you have on YOUGotaGift.com are built directly into YOUGotaGift.com or your settings. For example, you can:", "yourChoicesp1": "Edit personal information i.e. name and mobile phone number in your profile at any time.", "yourChoicesp2": "As stated previously, you can always opt not to disclose information, even though it may be needed to take advantage of or register for certain features of the Applications.", "yourChoicesp3": "You may request deletion of your Applications account by sending an e-mail to: <EMAIL>.", "yourChoicesp4": "In certain cases, if you decline to share personal information on the Applications, YOUGotaGift.com will not be able to provide certain services to you. You may also choose to opt-out of future marketing mailings by following the opt-out instructions in the emails that we send to you.", "ConditionsOfUse": "Conditions of Use, Notices, and Revisions", "ConditionsOfUseDesc": "If you use the Applications, your use and any dispute over privacy is subject to this Privacy Policy, our Terms of Use, including limitations on damages, arbitration of disputes, and Applications of the law of the United Arab Emirates. If you have any concern about privacy at YOUGotaGift.com, please contact us with a thorough description, and we will try our best to resolve it. Our business changes constantly, and our Privacy Policy and the Terms of Use Agreement will change also. You should check our website to see recent changes. Unless stated otherwise, our current Privacy Policy applies to all information that we have about you and your account.", "internationalTransferInfoCollection": "How we transfer information we collect internationally", "internationalTransferInfoCollectionDesc": "We collect information from all our users and store that information in the United Arab Emirates and United States of America. We transfer, process and store your information outside of your country of residence, to wherever we operate for the purpose of providing you the services. Whenever we transfer your information, we take steps to completely protect it.", "yourRighttoAccessData": "Your Right to Access Your Data", "yourRighttoAccessDataDesc": "You have the below mentioned options in relation to the information that we have collected about you. To exercise these options, please contact <NAME_EMAIL>. If you’re an European Union (EU) user, you can also:", "yourRighttoAccessDatap1": "Access the information we hold about you. We’ll usually share this with you within 30 days of you asking us for it.", "yourRighttoAccessDatap2": "Have your information corrected or deleted. You can update your information in your settings. If you have problems updating the information or if you would like us to delete it, contact <NAME_EMAIL>.", "yourRighttoAccessDatap3": "Object to us processing your information. You can ask us to stop using your information, including when we use your information to send you marketing emails or push notifications. We will only send you marketing material if you’ve agreed to it, but if you’d rather we don’t, you can easily unsubscribe at any time.", "yourRighttoAccessDatap4": "Have the information you provided to us sent to another organization, where we hold this information with your consent or for the performance of a contract with you, where it’s technically feasible.", "yourRighttoAccessDataFoot": "We retain the data we collect for as long as needed to provide you services and to maintain adequate records as per the law of country where we operate, resolve disputes, and meet our regulatory obligations.", "childrenPrivacy": "Children's Privacy", "childrenPrivacyDesc": "The Applications of YOUGotaGift.com are not structured to attract children. Accordingly, we do not intend to, nor will we knowingly collect personal information from anyone we know to be under 16 years of age. If you become aware that a child has provided us with personal information, please <NAME_EMAIL> with our support services.", "changesOfThisPolicy": "Changes to this Privacy Policy", "changesOfThisPolicyDesc": "YOUGotaGift.com may amend this Privacy Policy from time to time, and at any time. Use of information we collect now is subject to this Privacy Policy in effect at the time such information is used. If we make changes to this Privacy Policy or in the way we use the Personal Information, we will notify you by posting an announcement on our website or sending you an email. Your continued use of our Applications makes you bound by any changes to the Privacy Policy, whenever you use the our Applications after such changes have been first posted.", "contactUs": "Contact Us", "contactUsDesc": "You may contact us through the following means at the following addresses:", "contactUsPrivacy": "If you have questions or concerns about how your information is handled, please direct your inquiry to YOUGotaGift.com via <NAME_EMAIL>.", "byMail": "By Mail:", "companyName": "YOUGotaGift.com Ltd. ", "addressLine1": "G06, Loft Offices 3, Entrance B", "addressLine2": "Dubai Media City, Dubai – UAE", "addressLine3": "P.O. Box 502681", "termsOfUseOBold": "Terms Of Use", "termsOfUsePrecautions": "PLEASE READ THESE TERMS OF USE CAREFULLY BEFORE USING YOUGotaGift.com (the “Application”). BY USING THE APPLICATION, YOU ARE AGREEING TO BE BOUND BY THESE TERMS AND THE APPLICATION PRIVACY POLICY. THESE TERMS AND OUR PRIVACY POLICY SUPPLEMENT, BUT DO NOT OVERRIDE. IF YOU DO NOT AGREE TO THE FOREGOING, DO NOT USE THE APPLICATION.", "general": "General", "generalDesc": "YOUGotaGift.com (the “Application”) may be used for personal non-commercial use strictly in accordance with these Terms of Use (“Terms”) and our Privacy Policy.", "competentAge": "Competent to Enter Into a Binding Agreement", "competentAgeDesc": "By using the Application, you are representing that you are of legal age (18 years and over), legally competent to enter into a binding agreement and are not otherwise prohibited from using or receiving the Application pursuant to applicable law.", "prohibitedConduct": "Prohibited Conduct", "prohibitedConductDesc": "You agree that you will not use the Application for any revenue generating endeavor, commercial enterprise or other purpose for which it is not designed or intended, including any unauthorized non-commercial marketing and promotional campaigns, target or mass solicitation campaigns or political campaigning.", "servicesProvidedByAffiliates": "Services Provided by Affiliates and Third Parties", "servicesProvidedByAffiliatesDesc": "Some of the features may be provided by third parties - such as Facebook - and you may be subject to the terms of service of those third parties in addition to these Terms.", "compatibilityWithDevices": "Compatibility with Devices", "compatibilityWithDevicesDesc": "YOUGotaGift.com LTD does not warrant that the Application will be compatible or interoperable with your device or any other hardware, software or equipment installed on or used in connection with your device.", "changeOfApplication": "Changes to the Application", "changeOfApplicationDesc": "We may issue upgraded versions of the Application and may automatically upgrade the version of the Application on our website. We may change, expand and improve the Application. We may at any time cease to continue operating part or all or selectively disable certain aspects of the Application.", "carrierCharges": "Carrier Charges", "carrierChargesDesc": "You acknowledge and understand that when using the Application from your phone it will require and utilize phone service or data access. Carrier rates for phone and data may apply and you are responsible for any such charges.", "yourAccountInfo": "Your Account Information", "yourAccountInfoDescp1": "You are responsible for maintaining the confidentiality of your account and password and for restricting access to your device, and you agree to accept responsibility for all activities that occur under your account or password. It is your responsibility to advise us if you are aware of any unauthorized access to your account. YOUGotaGift.com LTD reserves the right to refuse service, terminate accounts, remove or edit content or cancel orders in its sole discretion.", "yourAccountInfoDescp2": "You are required to provide accurate and complete information when you create your account with us, and you are required maintain at all times true, accurate and complete information related to your account. We are under no obligation to retain a record of your account or any data or information that you may have stored by means of the account or your use of the Application. You are prohibited from utilizing alter-egos or other disguised identities when utilizing the Application. You are under no obligation to use or continue to use the Application and may cease use of the Application without notice to YOUGotaGift.com LTD.", "securityAndPrivacy": "Security and Privacy", "securityAndPrivacyDescp1": "Certain personal information and other information provided by you may be stored on your device even if such information is not collected by the Application. It is your responsibility to maintain the security of your device from unauthorized access. Use by the Application of any personal information about you is subject to our Privacy Policy.", "securityAndPrivacyDescp2": "While YOUGotaGift.com LTD makes commercially accepted efforts to maintain the security of customer transactions, its liability in cases of third party theft or misuse is limited to the funds which YOUGotaGift.com LTD has already collected in reference to the your most recent order.", "electronicCommunications": "Electronic Communications", "electronicCommunicationsDesc": "When you use the Application or send e-mails to us, you are communicating with us electronically. You consent to receive communications from us electronically. We will communicate with you by e-mail. You agree that all agreements, notices, disclosures and other communications that we provide to you electronically satisfy any legal requirement that such communications be in writing.", "trademarks": "Trademarks", "trademarksDesc": "YOUGotaGift.com and other marks we use in connection with the Application and our website are registered trademarks of YOUGotaGift.com LTD or its subsidiaries in the United Arab Emirates and other countries. In addition YOUGotaGift.com graphics, logos, page headers, button icons, scripts, and service names are trademarks or trade dress of YOUGotaGift.com LTD or its subsidiaries. YOUGotaGift.com’s trademarks and trade dress may not be used in connection with any product or service that is not YOUGotaGift.com’s, in any manner that is likely to cause confusion among customers, or in any manner that disparages or discredits YOUGotaGift.com. All other trademarks not owned by YOUGotaGift.com LTD or its subsidiaries that appear on this site are the property of their respective owners, who may or may not be affiliated with, connected to, or sponsored by YOUGotaGift.com LTD or its subsidiaries.", "copyrightCompanies": "Copyright/Trademark Complaints", "copyrightCompaniesDesc": "YOUGotaGift.com respects the intellectual property of others. If you believe that your work has been copied, or infringed, in a way that constitutes copyright infringement or trademark infringement, please follow our Notice and Procedure for Making Claims of Copyright Infringement set forth below.", "productDescriptions": "Product Descriptions", "productDescriptionsDesc": "YOUGotaGift.com attempts to be as accurate as possible. However, much of the product information associated with the Application is provided by third parties for which YOUGotaGift.com relies on to be accurate and non-misleading. Accordingly, YOUGotaGift.com does not warrant that the Application, product descriptions, and other content on this site are complete, accurate, non-misleading, reliable, current or error-free.", "returnsAndRefunds": "Returns and Refunds", "returnsAndRefundsDesc": "YOUGotaGift.com does not accept cashbacks, refunds or returns.", "honoringYourPaymentCommitments": "Honoring Your Payment Commitments", "honoringYourPaymentCommitmentsDesc": "When you make a purchase using the Application you are representing to us that (i) any credit information you supply is true and complete, (ii) charges incurred by you will be honored by your credit/debit card company, and (iii) you will pay the charges incurred by you at the posted prices, including any applicable taxes.", "unlawfulActivity": "Unlawful Activity", "unlawfulActivityDesc": "We reserve the right to investigate complaints or reported violations of this Agreement and to take any action we deem appropriate, including but not limited to reporting any suspected unlawful activity to law enforcement officials, regulators, or other third parties and disclosing any information necessary or appropriate to such persons or entities relating to your profile, email addresses, usage history, posted materials, IP addresses and traffic information.", "disclaimerOfWarranties": "Disclaimer Of Warranties And Limitation Of Liability", "disclaimerOfWarrantiesDescp1": "Any use of the Application shall be at your sole risk. YOUGotaGift.com disclaims any and all responsibility or liability for the accuracy, content, completeness, legality, reliability, or operability or availability of information or the material accessible by use of the Application.", "disclaimerOfWarrantiesDescp2": "THE APPLICATION IS PROVIDED ON AN “AS IS” AND “AS AVAILABLE” BASIS. YOUGotaGift.COM MAKES NO REPRESENTATIONS OR WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED, AS TO THE OPERATION OF THE APPLICATION. YOU EXPRESSLY AGREE THAT YOUR USE OF THE APPLICATION AND SERVICES IS AT YOUR SOLE RISK.", "disclaimerOfWarrantiesDescp3": "TO THE FULL EXTENT <PERSON><PERSON><PERSON><PERSON><PERSON>LE BY APPLICABLE LAW, <PERSON><PERSON><PERSON>G<PERSON>.COM DISCLAIMS ALL WARRANTIES, EXPRESS OR I<PERSON><PERSON>IED, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF NON-INF<PERSON>NGEMENT AND MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. YOUGotaGift.COM DOES NOT WARRANT THAT THE SERVICES YOUGotaGift.COM’S SERVERS, OR THE E-MAIL YOU RECEIVE AS A RESULT OF YOUR USE OF THE APPLICATION ARE FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS. YOUGotaGift.COM WILL NOT BE LIABLE FOR ANY DAMAGES OF ANY KIND ARISING FROM THE USE OF THE APPLICATION, INCLUDING, BUT NOT LIMITED TO DIRECT, INDIRECT, INCIDENTAL, PUNITIVE, AND CONSEQUENTIAL DAMAGES.", "disclaimerOfWarrantiesDescp4": "WE AND OUR AFFILIATES SHALL NOT BE LIABLE FOR ANY LOSS, INJURY, CLAIM, LIABILITY, OR DAMAGE OF ANY KIND RESULTING IN ANY WAY FROM (A) ANY ERRORS IN OR OMISSIONS OR ANY SERVICES OR PRODUC<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>LE FROM THE APPLICATION OR SERVICES, (B) THE UNAVAILABILITY OR INTERRUPTION OF THE APPLICATION OR SERVICES OR ANY FEATURES THEREOF, (C) YOUR USE OF THE APPLICATION OR SERVICES, (D) THE CONTENT CONTAINED IN OR ACCESSIBLE THROUGH THE APPLICATION OR SERVICES, OR (E) ANY DELAY OR FAILURE IN PER<PERSON>OR<PERSON>NCE BEYOND OUR CONTROL.", "disclaimerOfWarrantiesDescp5": "TO THE MAXIMUM EXTENT PERMITTED UNDER THE APPLICABLE LAW, YOUGotaG<PERSON>.COM WILL NOT BE LIABLE FOR ANY DAMAGES OF ANY KIND ARISING FROM THE USE OF THIS SITE, INCLUDING, BUT NOT LIMITED TO DIRECT, INDIRECT, INCI<PERSON><PERSON><PERSON>, PUNITIVE AND CONSEQUENTIAL DAMAGES.", "indemnification": "Indemnification", "indemnificationDesc": "You agree to indemnify, defend and hold us and our partners, agents, officers, directors, employees, subcontractors, successors and assigns, third party suppliers of information and documents, attorneys, advertisers, product and service providers and affiliates harmless from any liability, loss, claim and expense, including reasonable attorney’s fees, related to your violation of these Terms or use of the Application.", "applicableLaw": "Applicable Law", "applicableLawDesc": "By using the Application, you agree that the laws of the United Arab Emirates will govern these Terms and any dispute of any sort that might arise between you and YOUGotaGift.com LTD or its affiliates.", "miscellaneous": "Miscellaneous", "miscellaneousDesc": "The language in these Terms shall be interpreted as to its fair meaning and not strictly for or against any party. Should any part of these Terms be held invalid or unenforceable, that portion shall be construed consistent with applicable law and the remaining portions shall remain in full force and effect. To the extent that anything in or associated with the Application is in conflict or inconsistent with these Terms, these Terms shall take precedence. Our failure to enforce any provision of these Terms shall not be deemed a waiver of such provision nor of the right to enforce such provision. Our rights under these Terms shall survive any termination of your use of the Application.", "updatesOfTheseTerms": "Updates or Revisions to These Terms", "updatesOfTheseTermsDesc": "From time to time, we may post on our website revisions of existing policies or new policies. Please review them and they govern your use of the Application. We reserve the right to make changes to the Application and our policies at any time.", "contactingUs": "Contacting Us", "companyNameLTD": "YOUGotaGift.com LTD ", "termsOfUseAddLine1": "The Lofts, Building 3, Entrance B", "termsOfUseAddLine2": "Unit 4, Ground Floor", "termsOfUseAddLine3": "Dubai Media City, Dubai, U.A.E.", "termsOfUseAddLine4": "P.O. Box 502681", "byPhone": "By Phone:", "workingHours": "Everyday, 9:00 AM - 10:00 PM, UAE", "callCompanyNo": "Call +971 4 441 8473", "byEmail": "By Email:", "<EMAIL>": "<EMAIL>", "watchPreview": "Watch Preview", "setdate": "Set Date", "helpCenter": "Help Center", "loginToProceed": "Login to Proceed", "pleaseChooseSecurePassword": "Please choose a secure password", "minMaxLengthForNameRequired": "Minimum characters allowed for Name is 2 and max allowed is 26", "signInUsingGoogle": "Sign-in using Google account that you used for Sign-Up", "signInUsingFacebook": "Sign-in using Facebook account that you used for Sign-Up", "signInUsingApple": "Sign-in using Apple account that you used for Sign-Up", "userAlreadySignedUpUsingEmail": "User with email already exists, please enter Email to continue", "checkEmailForResetPassword": "Please check your email for password reset instructions", "requestSuccessful": "Request Successful", "orderSummary": "Order Summary", "totalQuantity": "Total Quantity", "items": "Items", "subTotal": "Sub Total", "serviceFee": "Service Fee", "totalToPay": "Total to Pay", "continueShopping": "Continue Shopping", "proceedToPay": "Proceed to Pay", "cartUserInfo": "Your Cart has been updated based on the current availability of the items.", "cartUserInfo2": "Please update the Gift Card details before proceeding to pay.", "inclusiveVAT": "Inclusive of VAT", "giftText": "Gift", "to": "To", "on": "On", "edit": "Edit", "remove": "Remove", "giftValue": "Gift Value", "vatOnServiceFee": "VAT on Service Fee", "orderDetails": "Order Details", "proceedCheckout": "Proceed to Checkout", "passwordAttemptsExceeded": "Password attempts exceeded, please try after some time or try resetting your password", "helpCentre": "Help Centre", "verifyEmail": "Verify Mail", "mailVerificationRequired": "Mail Verification Required!", "verifyMailToAccess": "Please verify your mail to access.", "outOfStockStatus": "Out of Stock", "denominationUnavailable": "Denomination unavailable", "confirm": "Confirm", "cancel": "Cancel", "deleteCartItemConfirm": "You are deleting an item from you cart. Do you want to proceed?", "deleteItem": "Delete Item", "emptyCart": "Your Gift Cart is empty", "startGifting": "Start Gifting", "emailAlreadyExistsWithAnotherUser": "User with email already exists, please enter a different Email to continue", "enterValidPassword": "Enter a valid password", "passwordDoNotMatch": "Password do not match.", "userAlreadySignedUpUsingGoogle": "Google account already exists with this email, please sign in using the same", "userAlreadySignedUpUsingApple": "Apple account already exists with this email, please sign in using the same", "userAlreadySignedUpUsingFacebook": "Facebook account already exists with this email, please sign in using the same", "newPassword": "New Password", "enterNewPassword": "Enter New Password", "confirmNewPassword": "Confirm New Password", "addYourInfo": "Add Your Info", "emptycartheading": "Gift from our most popular eGift Cards", "specialCharactersNotAllowedInName": "Special characters not allowed in Name", "profileUpdatedSuccessfully": "Profile updated successfully.", "wrongPassword": "Wrong Password", "passwordDoesntMatch": "Password doesn't match", "CurrentpasswordDoesntMatch": "Current Password doesn't match", "LimitExceeded": "Attempt limit exceeded, please try after some time.", "currentAndNewPasswordAreSame": "current password and new password are same", "passwordChangedSuccessfully": "Password Changed Successfully", "newPasswordRequired": "New password required", "usermessage": "Enter your message here.", "nameCannotBeEmpty": "Name cannot be empty", "errorUpdatingProfile": "Error updating profle.", "outOfStockNotification": "This eGift card is currently out of stock. Please try after sometime.", "deliveryon": "Delivery on", "delivery": "Delivery", "only15DigitsAllowed": "Only 15 are digits allowed", "verificationCodeLabel": "Verification Code", "characters": "Characters", "enterValidDob": "Enter valid date", "error": "Error", "userAuthentication": "User Authentication", "currentPassword": "Current Password", "reNewPassword": "Re-type New Password", "updatePassword": "Update Password", "personalInformation": "Personal Information", "gender": "Gender", "birthDate": "Birth Date", "mobileNo": "Mobile No", "saveChanges": "Save Changes", "contactInfo": "Contact Information", "backToHome": "Back to Home", "editProfile": "Edit Profile", "deleteAccount": "Delete Account", "deleteAccountText": "Are you sure you want to delete your account? If you delete your account, you will permanently lose your HappyCredits, saved Gift Cards and order history.", "changePassword": "Change Password", "countrytitle": "Region Not Supported", "desc1": "Sorry, this website is not available in your country.", "desc2": "We apologize for the inconvenience.", "brandSelect": "Choose an occasion", "gretingCover": "No greeting cover available !", "mr": "Mr.", "miss": "Miss.", "emailAssociatedWithAccount": "Email ID associated with an account", "verifyOrAnotherEmail": " or please enter another email id", "clickToVerify": "Click here to verify", "otpText": "Otp", "searchHere": "Search here...", "fullSecure": "100% Secure", "assistanceText": "For any further assistance please visit our", "viewMyOrders": "View My Orders", "orderSuccess": "Order Successfully Completed", "orderId": "Order ID: {{orderId}}", "orderFailMessage": "Your Order Was Not Placed!", "somethingWrong": "Something went wrong!", "goBack": "Go Back", "likeYouGotaGift": "Enjoying YOUGotaGift?", "reviewMessage": "Please take a minute and let us know how much you like us?", "mayBeLater": "Maybe Later", "rateNow": "Rate Now", "limitExceeded": "Limit Exceeded", "immediately": "Immediately", "slf": "Self", "youDontHavePassword": "You don’t have a password yet for this account. Either reset your password or continue with your Social account", "close": "Close", "photoFileSizeWarn": "The file is {{size}} MB exceeding the maximum file size of 15 MB.", "videoFileSizeWarn": "The file is {{size}} MB exceeding the maximum file size of 100 MB and the duration should be less than 30 seconds.", "videoDurationWarn": "Video duration should be less than 30 seconds.", "phoneNumberRequired": "Phone number is required", "nameRequired": "Name is required", "shareStory": "Share via story", "shareWithFriends": "Share with friends", "storyFooterNote": "Gift Card details not included while sharing", "getLink": "Get Link", "viewInWallet": "View in Wallet", "from": "From", "viewGreetingsAgain": "View Greetings Again", "sayThankYou": "Say Thank You", "messageNotEmpty": "Message Cannot be Empty", "sendMessage": "Send Message", "defaultThanksMessage": "Thank you for this lovely gift, \n I appreciate it.", "howToAccessCard": "How to access your eGift Card?", "howToAccessCardAnswer": "Your eGift Card is now available in your Gift Wallet. Download App to access it.", "sendDownloadLinkText": "Send the Download link to your phone:", "sendDownloadLinkContactText": "You will be asked to download YOUGotaGift App to access Gift Wallet. eGift Card was also sent to {{email}}", "readyForSurprise": "Are you ready for your Surprise from {{sender}}?", "openNow": "Open Now", "linkCopied": "Link copied to clipboard", "recaptchEnterOTPTitlePhone": "Enter the verification code sent to your phone number", "recaptchEnterOTPTitleEmailId": "Please enter the verification code sent to your email id to proceed", "recaptchEnterOTPTitleEmailAddress": "Please enter the verification code sent to your email address to continue.", "invalidCode": "Invalid Code", "enteVerificationCode": "Enter the verification code", "deliveryDateExpired": "Expired Delivery Date", "inclServiceFeeVAT": "Incl. of Service Fee & VAT", "baseAmount": "Base Amount", "vat": "VAT", "paymentPending": "Payment Pending!", "paymentPendingText": "You have a pending payment request. Do you want to proceed to the payment page to complete it?", "proceed": "Proceed", "unusualActivityDetected": "Unusual Activity Detected", "unusualActivityDesc": "We have deactivated your account as we have detected unusual activity. Please visit our <strong>Help Centre</strong> to reactivate your account.", "sharingExperience": "Thanks for sharing your experience!", "ratingSaved": "Your rating was saved", "tellUsMore": "Tell us more about your feedback", "feedbackThanks": "Thank you for sharing your valuable feedback.", "anonymousFeedback": "Your feedback will remain anonymous", "typeFeedback": "Type your feedback here", "iWasPleased": "I was pleased with the variety of gift cards. I got what I wanted", "notExactly": "Reasonable selection of items, but not exactly what I wanted!", "notGoodSelection": "Didn’t have a good selection of items to choose from!", "selectRating": "Please select your rating", "male": "male", "female": "female", "qrCodeWarning": "Sorry, this QR code is only compatible with iOS and Android devices.", "buyInStore": "Buy in Store", "choice": "Choice", "convenience": "Convenience", "digitalDelivery": "Digital Delivery", "acceptance": "Acceptance", "seeAllCards": "See all eGift Cards", "seeAllHappyCards": "See all HappyYOU Cards", "newsTitle": "Get Notified <strong> | </strong> New Brands & Offers", "inStoreOnly": "IN-STORE only", "inStore": "IN-STORE", "online": "ONLINE", "offersTitle": "Offers included in this card", "expiring": "Expiring", "offersIncluded": "Offers Included", "termsAndConditions": "Terms & Conditions", "brandsIncluded": "Brands Included", "all": "ALL", "areYouReadyForYourSurprise": "Are you ready for your Surprise Gift?", "organisedBy": "Organised by", "downloadYgagAppToAccessEgiftCard": "Download YOUGotaGift App to access the e-Gift Card", "eGiftCardAlsoSendTo": "eGift Card was also sent to {{email}}", "makeItPersonal": "Make it Personal!", "addVideoPhoto": "Add video, photo, greeting cover, stickers and much more to make it fun!", "activateYourGift": "Activate your eGift Card", "enterEmailToActivate": "Enter your email address to activate, receive and get redemption details for the eGift Card.", "activatingYourGiftcard": "Activating your eGift Card", "confirmYourEmail": "Please confirm your email address before proceeding. Further details will be shared on this email address", "weHaveSendVerficationCode": "We have sent the verification code to your mobile number ", "enterCodeToProceed": "Please enter the verification code to proceed.", "pleaseTryAgainIn": "Please try again in", "success": "Success!", "emailId": "Email ID", "other": "Other", "cardPurchaseSummaryTitle": "{{quantity}} eGift Cards | {{currency}} {{amount}} each", "addPhotoOrVideo": "Add Photo or Video", "changePhotoOrVideo": "Edit Photo/Video", "photoOrVideo": "Photo or Video", "addPhoto": "Add Photo", "addVideo": "Add Video", "receiverAndDelivery": "Receiver & Delivery", "add": "Add", "skipped": "Skipped", "deliverydetails": "Delivery Details", "receiverName": "Receiver Name", "receiverEmail": "Receiver <PERSON><PERSON>", "receiverMobileNumber": "Receiver Mobile Number", "deliver": "Deliver", "preview": "Preview", "greetingsPreview": "Greetings Preview", "mediaPreview": "Media Preview", "thankYou": "Thank You", "oops": "Oops!", "viewEGiftCard": "View eGift Card", "buyNow": "Buy Now", "checkout": "Checkout", "invalidEmailId": "Invalid Email ID", "ok": "OK", "editPhotoOrVideo": "Edit photo/video", "invalidBrandAlertMessage": "Looks like this brand is unavailable or sold out. Please try again after some time.", "eGiftNotAvailable": "eGift Card Not Available", "removeHTMLTagsMessage": "Please remove HTML tags from the message.", "error-text": "Error. Please try again after some time.", "nameBeginWithLetter": "Name must begin with a letter", "maxLengthFeedback": "Only 1000 Characters allowed", "resend": "Resend", "resendingOtp": "Resending OTP...", "noInstoreDataAvailable": "No \"In-Store\" brands available.", "noOnlineDataAvailable": "No \"Online\" brands available.", "unsupportedPlatform": "Unsupported Platform", "inactive": "Inactive", "removePhotoOrVideo": "Remove Photo / Video", "sureYouWantToRemoveIt": "Are you sure you want to remove it?", "replay": "Replay", "giftsOrdered": "Gifts Ordered", "giftsRecieved": "Gifts Recieved", "giftsRedeemed": "Gifts Redeemed", "giftsExpired": "Gifts Expired", "downloadAllGiftasPDF": "Download all Gifts as PDF", "totalItem": "Total Items", "totalGiftValue": "Total Gift Value", "serviceFeeincVAT": "Service Fee inc. VAT", "total": "Total", "paymentMethod": "Payment Method", "cardEnding": "Card ending in", "giftsTitle": "Gifts Ordered({{count}} Items)", "viewItems": "View Items", "serviceVat": "Service Fee incl. VAT", "value": "Value", "item": "<PERSON><PERSON>", "id": "ID", "importantNote": "Important Notice!", "giftMessage": "Any orders made after May 10, 2023, will be displayed here. We are currently processing the order history before this date and it will be accessible soon. Should you have any pressing inquiries about your order history, we invite you to visit our", "center": "Help Center", "downloadPDF": "Download PDF", "share": "Share", "qty": "Qty. <strong>{{ qty }}</strong>", "noGiftsOrderd": "No Gifts Ordered", "notOrderedGiftYet": "You have not ordered any gifts yet!", "getPrintablePDF": "Get Printable PDF", "howItWork": "How it work", "downloadSamplePDF": "Download Sample PDF", "sendToRecipient": "Send to Recipient", "printablePDF": "Printable PDF", "pdfProcessing": "PDF Processing...", "downloadAppHeaderExitingFeatures": "To access the Gift wallet & other exciting features", "downloadYougotagiftApp": "Download the YOUGotaGift App", "reviewTitle": "We’ve got your back from day one", "pastWeek": "in past week", "giftsSend": "Gifts Sent", "review": "reviews", "helpAppreciation": "Helpline Appreciation", "seeGoogle": "See it on Google", "recordFromCamera": "Record from Camera", "uploadAFile": "Upload a file", "allowHardwareAccess": "Allow access to the camera on this device", "allowHardwareAccessMessage": "If you allow access, people using this device will be able to choose if their apps have camera access by using the settings on this page.", "pdf": "pdf", "dataDeletionInstruction": "Data Deletion Instructions", "dataDeletionInstructionMessage": "<strong>{{ecomUrl}}</strong> is a Gifting platform and we do not save your personal data in our server. But according to the Facebook Platform rules, we have to provide User Data Deletion Callback URL or Data Deletion Instructions URL. If you want to delete your activities for YOUGotaGift App, you can remove your activities by the following instructions.", "dataDeletionInstructionStep1": "Go to Your Facebook Account’s Settings & Privacy.Click ”Setting“.", "dataDeletionInstructionStep2": "Then, go to ”Apps and Websites” and you will see all of your App activities.", "dataDeletionInstructionStep3": "Select the option box of YOUGotaGift.", "dataDeletionInstructionStep4": "Click the ”Remove” button.", "dataDeletionInstructionStep5": "Congratulations, you have successfully removed your activities.", "exampleMessage": "This is an example with an <0>anchor tag</0> within the string.", "logOutGuestUser": "Log out from Guest account", "senderName": "Sender Name", "sendBy": "Sent by", "loginGuestUserMessageGroupGift": "Please Login or Signup to start a Group Gift", "loginRequired": "Login/Signup Required", "loginGuestUserMessageSpecialBrands": "You need to Sign up or Login to purchase the gift you have selected", "buyThisCard": "Buy this Card to enjoy all these offers", "guestUser": "Guest User", "upload": "Upload", "uploading": "Uploading...", "uploaded": "Uploaded", "recordAgain": "Record again", "specialOfferIncluded": "Special Offers from Brands included", "buyCardAndEnjoySpecialOffer": "Buy this {{card}} and enjoy these <strong>{{count}} Special Offers</strong> when you spend it at these Brands", "offerTitle": "Buy {{brandName}} & enjoy", "offerCard": "on {{brandName}} eGift Cards", "onOfferBrand": "on {{brandName}}", "worksTitle": "How it Works?", "worksText": "Buy a {{brandName}}", "spendText": "Spend it to get {{brandName}} eGift Card", "valueText": "Enjoy Extra Value on your {{brandName}} eGift Card", "promoText": "Enjoy Promo Code with your {{brandName}} eGift Card", "termsTitle": "Terms & Conditions Apply", "mediaCenter": "Media Centre", "readMoreOn": "Read More On", "offerValidity": "Offer Validity", "applicableAmounts": "Applicable to Amount", "searchBrand": "Search brands here", "noBrand": "No Brands found for", "spellingError": "Please ensure that there are no spelling errors", "skip": "<PERSON><PERSON>", "loginGuestUserMessagePdfSelected": "You need to Sign<PERSON> or <PERSON><PERSON> to purchase the gift as printable PDF", "selectGreetingCover": "Please select an occasion to view Greeting Covers.", "completeOrder": "Please try again to complete your order.", "aiText": "Generate Message with AI", "aiGenerate": "Generate Message", "specialNote": "Special note to be included (Optional)", "selectRelationship": "Select Relationship with Recipient", "useMessage": "Use This Message", "generateMessage": "Generate Message", "sayThanks": "Say Thanks", "skipToGift": "Skip to <PERSON>", "youReacted": "You Reacted", "thanked": "Thanked", "writeThankYouMessage": "Write Thank You Message...", "accountDeletionTitle": "Account Deletion", "accountDeletedSuccessfully": "User account deleted successfully", "unableToDeleteUserAccount": "Unable to delete user account", "browserByCategories": "Browse By Categories", "in": "in", "offerNotAvailable": "OFFER NOT AVAILABLE", "suspendedInfo1": "Let's get you back online!", "suspendedInfo2": "Please contact our customer support team to reactivate your account.", "accountSuspended": "Account suspended due to unusual activity", "reactivateAccount": "Reactivate My Account", "categories": "Categories", "occasion": "Occasion", "whoisitfor": "Who is it for?", "clearFilter": "Clear Filter", "clear": "Clear", "Filters": "Filters", "cookieDesc": "In order for our website to function correctly you must configure your browser to accept cookies", "addGif": "Add GIF", "noResults": "No Results Found !", "searchGif": "Search GIFs here", "updateMobileNo": "Update Mobile Number", "updateMobileNoDesc": "If you change the phone number linked with this account, all the future communications (including OTP and gift messages) will be sent to the new number. Do you want to continue?", "verificationCode": "Verification Code", "verificationCodeDesc": "We have sent the verification code to your mobile number <0>{{number}}</0> & email <0>{{email}}</0>", "verificationCodeDesc2": "We have sent the verification code to your mobile number <0>{{number}}</0>", "country": "Country", "resendSms": "Resend OTP SMS", "resendWhatsApp": "Send OTP on whatsapp", "resendCodeIn": "Resend code in", "tryAgainLater": "Please try again later", "okay": "Okay", "offer": "OFFER", "offerExhausted": "This offer has been exhausted but will be back soon", "unlockPromo": "Unlock promo code for exclusive benefits", "support": "Support", "spreadingHappiness": "Spreading Happiness", "haveQuestion": "Have questions?", "checkOutOur": "check out our", "download": "Download", "yougotagiftApp": "YOUGotaGift App", "forDevelopers": "For Developers", "home": "Home", "langSwitch": "AR", "gifting": "Gifting", "groupGifting": "Group Gifting", "work": "Work", "searchCountry": "Search your country ", "clearAll": "Clear All", "qty.": "Qty.", "excitingNewOffers": "Exciting new offers are on the way!", "getHappyCardNow": "Get your HappyYOU Card now, and you’ll be the first to access the latest deals as soon as they’re added.", "egiftcardoffer": "eGift Card offer", "extravalueoffers": "Extra Value Offers", "promocodeoffers": "Promo Code Offers", "buyunlockoffers": "Buy {{name}} & unlock all these offers", "promocodes": "Promo Codes", "selectMokafaPoint": "At checkout select {{payment}} points as payment method", "payUsingMokafa": "Pay for the transaction using {{payment}} points", "expiresOn": "EXPIRES ON", "giftingCart": "Gifting Cart", "subtotal": "Subtotal", "totalCards": "Total Cards", "totalValue": "Total Value", "servFeeCart": "Service Fee (inc. of VAT)", "removeItem": "Remove Item?", "toSelf": "To Self", "offersCart": "Offers on your cart items", "login": "<PERSON><PERSON>", "noSavedCards": "You don’t have any Saved Cards right now!", "yourCards": "Your Cards", "deleteCard": "Delete Card", "cardRemoveConfirm": "Are you sure you want to remove this card?", "emptyOrder": "You don’t have any Orders right now!", "changeLanguage": "Change Language", "myAccount": "My Account", "savedCards": "Saved Cards", "logout": "Logout", "logoutFromGuest": "<PERSON><PERSON><PERSON> from Guest", "gaming": "Gaming", "ourCompany": "Our Company", "giftCardSolutions": "Gift Card Solutions", "reactionSent": "Reaction Sent!", "buy": "Buy", "happyYouOffers": "HappyYOU Offers", "of": "Of", "pageNotFound": "Page not found", "aboutHappyYou": "About HappyYOU", "happyYouTerms": "HappyYOU Terms & Condition", "buyHappyYou": "Buy HappyYOU", "termsAndCondition": "{{brandName}} Terms & Condition", "aboutBrandCard": "About {{brandName}} Gift Card", "hiring": "Hiring", "new": "New", "expoloreHub": "Explore Gift Card Solutions", "solutionHub": "Solutions Hub", "behindScenes": "Behind The Scenes", "totalGift": "Total Gift Card Solutions for Buyers & Brands.", "powering": "Powering Gift Card Processing & Distribution", "downloaApp": "Download Our App.", "enjoyExp": "Enjoy a Total Experience."}