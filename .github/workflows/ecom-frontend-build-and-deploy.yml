name: Build & Deploy
run-name: Deploying ecom-frontend ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        default: 'sandbox'
        type: choice
        options:
        - sandbox
        - production
      branch:
        description: 'Branch'
        type: string
      prod_hotfix_branch:
        description: 'Deploy Production with hot-fix branch'
        default: false
        type: boolean

jobs:
  
  deploy:
    name: ecom-frontend-${{ inputs.environment }}
    uses: YouGotaGift/devops-organization-actions/.github/workflows/ap-south-1-frontend-application-build-and-deploy.yaml@main
    with:
      application_name: ecom-frontend
      environment: ${{ inputs.environment }}
      branch: ${{ inputs.branch }}
      prod_hotfix_branch: ${{ inputs.prod_hotfix_branch }}
      app_image_repository: ecom
    secrets: inherit
