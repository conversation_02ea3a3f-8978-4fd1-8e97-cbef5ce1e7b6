name: Build & Deploy - Sandbox

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        default: 'sandbox'
        type: choice
        options:
        - sandbox

env:
  APPLICATION_NAME: ecom-frontend
  APP_IMAGE_REPOSITORY: production/ygag/ecom/frontend-app
  NGINX_IMAGE_REPOSITORY: production/ygag/ecom/frontend-nginx
  ECR_REGISTRY: 459037613883
  ECR_REGION: ap-south-1
  ENVIRONMENT: ${{ inputs.environment }}
  VAULT_ADDRESS: ${{ secrets.VAULT_ADDRESS }}
  PRODUCT_TEAM: frontend
  NGINX_BASE_IMAGE: '459037613883.dkr.ecr.ap-south-1.amazonaws.com/production/ygag/custom/nginx:1.16.1-1'


jobs:

  build-app:
    name: Build App
    runs-on: self-hosted
    steps:

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2.2.0
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_ECR_PUSH }}
          aws-secret-access-key: ${{ secrets.AWS_ACCESS_SECRET_ECR_PUSH }}
          aws-region: ${{ env.ECR_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1.6.0
        with:
          registries: ${{ env.ECR_REGISTRY }}
          registry-type: private
          region: ${{ env.ECR_REGION }}

      - name: Checkout Code
        uses: actions/checkout@v3.0.2
        with:
         ref: pre-production

      - name: Import Secrets
        uses: hashicorp/vault-action@v2.6.0
        with:
          tlsSkipVerify: true
          url: https://shamir-internal.yougotagift.co:8200
          token: ${{ secrets.VAULT_TOKEN }}
          caCertificate: ${{ secrets.VAULT_CERT }}
          secrets: |
            ygag-secrets/data/${{ env.PRODUCT_TEAM }}/${{ env.ENVIRONMENT }}/${{ env.APPLICATION_NAME }}/env app-env | ENV_VARS

      - name: Setting up Env Variables
        run: |
          cat <<< "$ENV_VARS" > application-env.sh

      - name: Docker Build and push
        env:
          CONTAINER_REGISTRY: ${{ env.ECR_REGISTRY }}.dkr.ecr.${{ env.ECR_REGION }}.amazonaws.com
        run: |
          IMAGE_TAG=$(git rev-parse --short HEAD)-${GITHUB_RUN_ID}-${{ env.ENVIRONMENT }}
          timestamp=$(date +%s)

          date_file=$(date +%s%N)
          touch $date_file

          DOCKER_BUILDKIT=0 docker build --no-cache -f deployment/app/Dockerfile -t $CONTAINER_REGISTRY/${{ env.APP_IMAGE_REPOSITORY }}:$IMAGE_TAG -t $CONTAINER_REGISTRY/${{ env.APP_IMAGE_REPOSITORY }}:${{ env.ENVIRONMENT }}_$timestamp --build-arg CACHEBUST=$date_file .
          docker push "$CONTAINER_REGISTRY/${{ env.APP_IMAGE_REPOSITORY }}:$IMAGE_TAG"
          docker push "$CONTAINER_REGISTRY/${{ env.APP_IMAGE_REPOSITORY }}:${{ env.ENVIRONMENT }}_$timestamp"

  build-nginx:
    name: Build Nginx
    runs-on: ubuntu-latest
    steps:

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2.2.0
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_ECR_PUSH }}
          aws-secret-access-key: ${{ secrets.AWS_ACCESS_SECRET_ECR_PUSH }}
          aws-region: ${{ env.ECR_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1.6.0
        with:
          registries: ${{ env.ECR_REGISTRY }}
          registry-type: private
          region: ${{ env.ECR_REGION }}

      - name: Checkout Code
        uses: actions/checkout@v3.0.2
        with:
         ref: pre-production

      - name: Docker Build and push
        env:
          CONTAINER_REGISTRY: ${{ env.ECR_REGISTRY }}.dkr.ecr.${{ env.ECR_REGION }}.amazonaws.com
        run: |
          IMAGE_TAG=$(git rev-parse --short HEAD)-${GITHUB_RUN_ID}-${{ env.ENVIRONMENT }}
          timestamp=$(date +%s)
          
          cd deployment/nginx/${{ env.APPLICATION_NAME }}/
          docker build -f Dockerfile -t $CONTAINER_REGISTRY/${{ env.NGINX_IMAGE_REPOSITORY }}:$IMAGE_TAG -t $CONTAINER_REGISTRY/${{ env.NGINX_IMAGE_REPOSITORY }}:${{ env.ENVIRONMENT }}_$timestamp --build-arg BASE_IMAGE="${{ env.NGINX_BASE_IMAGE }}" --build-arg ENVIRONMENT="${{ env.ENVIRONMENT }}" .
          docker push "$CONTAINER_REGISTRY/${{ env.NGINX_IMAGE_REPOSITORY }}:$IMAGE_TAG"
          docker push "$CONTAINER_REGISTRY/${{ env.NGINX_IMAGE_REPOSITORY }}:${{ env.ENVIRONMENT }}_$timestamp"

  deploy-app-nginx:
    needs: [build-app, build-nginx]
    name: Deploy
    runs-on: self-hosted
    steps:
     - name: Checkout Code
       uses: actions/checkout@v3.0.2
       with:
         ref: pre-production

     - name: Setup Dynamic env variables
       run: echo "IMAGE_TAG=$(git rev-parse --short HEAD)-${GITHUB_RUN_ID}-${{ env.ENVIRONMENT }}" >> $GITHUB_ENV

     - name: Setup Production env variable
       if: env.ENVIRONMENT  == 'production'
       run: |
        echo "KUBERNETES_HOST=${{ secrets.YGAG_AP_SOUTH_1_PRODUCTION_KUBERNETES_HOST }}" >> $GITHUB_ENV
        echo "KUBERNETES_CLUSTER_CA_CERT=${{ secrets.YGAG_AP_SOUTH_1_PRODUCTION_KUBERNETES_CLUSTER_CA_CERT }}" >> $GITHUB_ENV
        echo "KUBERNETES_TOKEN=${{ secrets.PRODUCTION_KUBERNETES_TOKEN }}" >> $GITHUB_ENV
        echo "DOMAIN=${{ secrets.PRODUCTION_DOMAIN }}" >> $GITHUB_ENV
        echo "CERTIFICATE_ARN=${{ secrets.PRODUCTION_CERTIFICATE_ARN }}" >> $GITHUB_ENV
        echo "WAF_ARN=${{ secrets.YGAG_AP_SOUTH_1_PRODUCTION_WAF_ARN }}" >> $GITHUB_ENV
        echo "ALB_ACCESS_LOG_BUCKET=${{ secrets.YGAG_AP_SOUTH_1_PRODUCTION_ALB_ACCESS_LOG_BUCKET }}" >> $GITHUB_ENV
     - name: Setup sandbox env variable
       if: env.ENVIRONMENT == 'sandbox'
       run: |
          echo "KUBERNETES_HOST=${{ secrets.YGAG_AP_SOUTH_1_SANDBOX_KUBERNETES_HOST }}" >> $GITHUB_ENV
          echo "KUBERNETES_CLUSTER_CA_CERT=${{ secrets.YGAG_AP_SOUTH_1_SANDBOX_KUBERNETES_CLUSTER_CA_CERT }}" >> $GITHUB_ENV
          echo "KUBERNETES_TOKEN=${{ secrets.SANDBOX_KUBERNETES_TOKEN }}" >> $GITHUB_ENV
          echo "DOMAIN=${{ secrets.SANDBOX_DOMAIN }}" >> $GITHUB_ENV
          echo "CERTIFICATE_ARN=${{ secrets.SANDBOX_CERTIFICATE_ARN }}" >> $GITHUB_ENV
          echo "WAF_ARN=${{ secrets.YGAG_AP_SOUTH_1_SANDBOX_WAF_ARN }}" >> $GITHUB_ENV
          echo "ALB_ACCESS_LOG_BUCKET=${{ secrets.YGAG_AP_SOUTH_1_SANDBOX_ALB_ACCESS_LOG_BUCKET }}" >> $GITHUB_ENV
     - name: Checkout template
       uses: actions/checkout@v3
       with:
          repository: yougotagift/devops-cd
          ref: 'main'
          ssh-key: ${{ secrets.DEVOPS_CD_DEPLOYMENT_KEY }}
          path: devops-cd

     - name: Search and replace values
       run: |
        cd devops-cd
        mkdir -p build/${{ env.APPLICATION_NAME }}
        cp application/* build/${{ env.APPLICATION_NAME }}
        cp ../deployment/values/${{ env.APPLICATION_NAME }}/${{ env.ENVIRONMENT }}/helm/* build/${{ env.APPLICATION_NAME }}
        cp ../deployment/values/${{ env.APPLICATION_NAME }}/${{ env.ENVIRONMENT }}/terraform/* build/${{ env.APPLICATION_NAME }}
        
        find ./ -type f -exec sed -i 's/\[ENVIRONMENT\]/${{ env.ENVIRONMENT }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[KUBERNETES_TOKEN\]/${{ env.KUBERNETES_TOKEN }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[VAULT_TOKEN\]/${{ secrets.VAULT_TOKEN }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[BUILD_TAG\]/${{ env.IMAGE_TAG }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[NGINX_BUILD_TAG\]/${{ env.IMAGE_TAG }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[IMAGE_PULL_SECRET\]/${{ env.IMAGE_PULL_SECRET }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[APPLICATION_NAME\]/${{ env.APPLICATION_NAME }}/g' '{}' \;
        find ./ -type f -exec sed -i 's/\[KUBERNETES_CLUSTER_CA_CERT\]/${{ env.KUBERNETES_CLUSTER_CA_CERT }}/g' '{}' \;
        find ./ -type f -exec sed -i 's!\[VAULT_ADDRESS\]!${{ env.VAULT_ADDRESS }}!g' '{}' \;
        find ./ -type f -exec sed -i 's!\[KUBERNETES_HOST\]!${{ env.KUBERNETES_HOST }}!g' '{}' \;
        find ./ -type f -exec sed -i 's!\[DOMAIN\]!${{ env.DOMAIN }}!g' '{}' \;
        find ./ -type f -exec sed -i 's!\[CERTIFICATE_ARN\]!${{ env.CERTIFICATE_ARN }}!g' '{}' \;
        find ./ -type f -exec sed -i 's!\[WAF_ARN\]!${{ env.WAF_ARN }}!g' '{}' \;
        find ./ -type f -exec sed -i 's!\[ALB_ACCESS_LOG_BUCKET\]!${{ env.ALB_ACCESS_LOG_BUCKET }}!g' '{}' \;

     - name: Installing Terraform
       uses: hashicorp/setup-terraform@v2
       with:
         terraform_version: 1.2.7

     - name: Configure AWS credentials
       uses: aws-actions/configure-aws-credentials@v2.2.0
       with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_ECR_PUSH }}
        aws-secret-access-key: ${{ secrets.AWS_ACCESS_SECRET_ECR_PUSH }}
        aws-region: us-east-1

     - name: Login to Amazon ECR helm registry
       id: login-ecr
       uses: aws-actions/amazon-ecr-login@v1.6.0
       with:
         registries: 459037613883
         registry-type: private
         region: us-east-1

     - name: Deploying application
       env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_ECR_PUSH }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_ACCESS_SECRET_ECR_PUSH }}
          VAULT_ADDRESS: ${{ secrets.VAULT_ADDRESS }}
          VAULT_TOKEN: ${{ secrets.VAULT_TOKEN }}
       run: |
         cd devops-cd/build/${{ env.APPLICATION_NAME }}
         aws ecr get-login-password --region us-east-1 | helm registry login --username AWS --password-stdin 459037613883.dkr.ecr.us-east-1.amazonaws.com
         terraform init
         terraform apply -auto-approve
