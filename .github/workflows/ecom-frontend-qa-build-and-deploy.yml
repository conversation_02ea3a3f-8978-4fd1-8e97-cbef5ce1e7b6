name: QA Build & Deploy ecom-frontend
run-name: QA Branch ${{ inputs.action }} ${{ inputs.branch }}

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

  repository_dispatch:
    types: [ "QA Build & Deploy ecom-frontend" ]
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

jobs:

  deploy:
    name: QA
    uses: YouGotaGift/devops-organization-actions/.github/workflows/qa-frontend-application-build-and-deploy.yml@main
    with:
      application_name: ecom-frontend
      branch: ${{ inputs.branch || github.event.client_payload.branch }}
      app_image_repository: ecom
    secrets: inherit
