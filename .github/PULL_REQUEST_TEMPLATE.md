## Task / Bug description
Clearly and concisely describe the task / bug details
## Issue ticket number and link
Provide JIRA id and link if available
## Root cause (Applicable for bugs only)
Briefly describe the root cause and analysis of the problem
## Solution description
Describe your code changes in detail for reviewers. Explain the technical solution you have provided.

## Checklist before requesting a review
<!-- Remove items that do not apply. For completed items, change [ ] to [x]. -->
- [ ] I have performed a self-review of my code
- [ ] If it is a core feature, I have added/updated unit tests.
- [ ] Issue tested in all browsers (Safari, Chrome, Firefox) if applicable.
- [ ] Local production build was a success.
