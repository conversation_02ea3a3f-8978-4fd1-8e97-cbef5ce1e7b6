import styles from "./DataDeletionInstruction.module.scss";
import { Trans, useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import useAppRouter from '@features/common/router.context';

const {
  publicRuntimeConfig: { basePath, ecomHostDomain },
} = getConfig();

const DataDeletionInstruction = (): JSX.Element => {
  //translations
  const { t } = useTranslation("common");
  const { router } = useAppRouter();

  // #.Constructed ecom base url
  const ecomUrl = `${ecomHostDomain}${basePath}/${router?.locale}/`;

  // #.Translation with dynamic URL
  const deletionInstructions = t('dataDeletionInstructionMessage', {
    ecomUrl,
  });

  return (
    <div className={styles["DataDeletionInstruction"]}>
      {/* <h3 className={styles["DataDeletionInstruction__title"]}>{t("dataDeletionInstruction")}</h3> */}

      <p className={styles["DataDeletionInstruction__contents"]}>
        <Trans
          i18nKey={deletionInstructions}
          components={{
            strong: <a href={ecomUrl}></a>,
          }}
        />
      </p>

      <ul className={styles["DataDeletionInstruction__steps"]}>
        <li className={styles["DataDeletionInstruction__steps-item"]}>
          <p className={styles["DataDeletionInstruction__contents"]}>
            {t("dataDeletionInstructionStep1")}
          </p>
        </li>
        <li className={styles["DataDeletionInstruction__steps-item"]}>
          <p className={styles["DataDeletionInstruction__contents"]}>
            {t("dataDeletionInstructionStep2")}
          </p>
        </li>
        <li className={styles["DataDeletionInstruction__steps-item"]}>
          <p className={styles["DataDeletionInstruction__contents"]}>
            {t("dataDeletionInstructionStep3")}
          </p>
        </li>
        <li className={styles["DataDeletionInstruction__steps-item"]}>
          <p className={styles["DataDeletionInstruction__contents"]}>
            {t("dataDeletionInstructionStep4")}
          </p>
        </li>
        <li className={styles["DataDeletionInstruction__steps-item"]}>
          <p className={styles["DataDeletionInstruction__contents"]}>
            {t("dataDeletionInstructionStep5")}
          </p>
        </li>
      </ul>
    </div>
  );
};
export default DataDeletionInstruction;
