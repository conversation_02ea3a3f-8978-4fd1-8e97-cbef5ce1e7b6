@import "/src/styles/variables";
@import "/src/styles/mixins";

.media-center {
    width: 100%;
    padding-right: 5px;
    padding-top: 12px;

    @include rtl-styles {
        padding-right: 0px;
        padding-left: 30px;
      }
    
    &-wraper {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    &-title {
        font-size: 24px;
        color: $dark-purple;
        margin: 15px 0;
        font-style: normal;
        font-weight: 800;
        line-height: 32px;
    }

    &-card {
        margin-bottom: 40px;

        &-date {
            font-size: 14px !important;
            color: #545454;
            font-weight: 400 !important;
        }

        &-title {
            font-size: 18px !important;
            color: $dark-purple;
            font-weight: 600 !important;
            line-height: 27px;
            margin-top: 10px !important;
            margin-bottom:20px !important;
            text-align: left !important;
            margin-right: auto !important;
            margin-left: 0  !important;

            &-ar{
                text-align: right !important;
                margin-left: auto !important;
                margin-right: 0  !important;
                font-family: $arabic-font-family !important;
                font-weight: bold !important;
            }

            @include rtl-styles {
                &-ar {
                    font-family: $arabic-font-family !important;
                }
                font-family: $default-font-family !important;
            }
        }

        &-button {
            font-size: 14px !important;
            color: $barney-purple !important;
            border-color: $barney-purple !important;
            padding: 10px 10px !important;
            text-transform: none !important;
            border-radius: 6px !important;
        }
    }

    &__image {
        border-radius: 12px;
        object-fit: cover;
    }
}