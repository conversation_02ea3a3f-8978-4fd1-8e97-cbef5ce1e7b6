import * as React from 'react';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import getConfig from "next/config";
import styles from "./MediaCenter.module.scss"
import { useTranslation } from "next-i18next";
import Image from 'next/image';

// #. Taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const MediaCard = ({
    date,
    image,
    title,
    button,
    ar,
    link }: any): JSX.Element => {

    //translations
    const { t } = useTranslation("common");


    return (
        <Box className={styles["media-center-card"]} sx={{ width: '47.5%', }}>
            <a href={link} target='_blank'>
            <Image
              className={styles["media-center__image"]}
              src={`${imageBaseUrl}/images/mediaCenter/${image}`}
              width={462}
              height={252}
            />
            </a>
            <Box
                sx={{
                    backgroundColor: 'transparent',
                    padding: '20px 0',
                }}
            >
                <Typography className={styles["media-center-card-date"]} align={ar ? 'right' : 'left'}>
                    <a href={link} target='_blank'>
                        {date}
                    </a>
                </Typography>
                <Typography className={`${styles["media-center-card-title"]} ${ar && `${styles["media-center-card-title-ar"]}`}`}>
                    <a href={link} target='_blank'>
                        {title}
                    </a>
                </Typography>
                <a href={link} target='_blank'>
                    <Button className={styles["media-center-card-button"]} variant="outlined">
                        {t("readMoreOn")} {button}
                    </Button>
                </a>
            </Box>
        </Box>
    );
}

export default MediaCard