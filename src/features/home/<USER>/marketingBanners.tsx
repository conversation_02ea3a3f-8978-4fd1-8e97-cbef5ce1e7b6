import React, { useEffect, useRef, useState } from 'react';
import styles from './marketingBanners.module.scss'
import ImageSlider from '@features/common/imageSlider/ImageSlider';
import { homeBannerDataQuery } from '../homeAPI';
import useAppRouter from '@features/common/router.context';
import { BANNER_TYPE } from '@constants/common';
import getConfig from 'next/config';
import Link from 'next/link';
import MarketingBannerSkeleton from '@features/allBrands/contentLoader/marketingBannerSkeleton/MarketingBannerSkeleton';
import gsap from 'gsap';

const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const MarketingBanners = () => {
    const {
        state: { region, locale },
    } = useAppRouter();

    const bannerRef = useRef(null);

    const [sliderImages, setSliderImages] = useState([]);
    const [mainBanner, setMainBanner] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (region) {
            getData();
        }
    }, [region]);

    useEffect(() => {
        if (!loading) {
            const banner = bannerRef.current;

            // Initial rotation along X-axis
            gsap.set(banner, {
                transformOrigin: 'top center',
                rotationX: -30,
            });

            // Smooth swing back to 0°
            gsap.to(banner, {
                rotationX: 0,
                duration: 2.2, // a bit slower
                ease: 'power4.out', // smooth and decelerating
            });
        }
    }, [loading]);

    const getData = async () => {
        const mainBannerQuery = homeBannerDataQuery(region, locale, BANNER_TYPE.MAIN);
        const sideBannerQuery = homeBannerDataQuery(region, locale, BANNER_TYPE.SIDE)
        const [mainBannerData, sideBannerData] = await Promise.all([mainBannerQuery, sideBannerQuery]);
        setSliderImages(sideBannerData?.data?.banners || []);
        setMainBanner(mainBannerData?.data?.banners?.[0] || null);
        setLoading(false);
    }

    return (
        <>
            {loading && <MarketingBannerSkeleton />}
            {!loading && (
                <div className={`${styles['marketing-banners']}`} >
                    <div className={`${styles['single-banner']}`} ref={bannerRef} style={{ perspective: 1000 }} >
                        {
                            <>
                                {mainBanner && <img src={`${mainBanner?.['imageWebp']}`} />}

                                {mainBanner?.['hasButton'] &&
                                    <Link href={`${mainBanner?.['url']}`}>
                                        <div className={`${styles['single-banner__button']}`}>
                                            <span>
                                                {mainBanner?.['buttonText']}
                                            </span>
                                            <img src={`${imageBaseUrl}/images/line-arrow-right.svg`} />
                                        </div>
                                    </Link>
                                }
                            </>

                        }
                    </div>
                    {sliderImages && sliderImages.length > 0 && <ImageSlider images={sliderImages} />}
                </div>
            )}

        </>

    );
};

export default MarketingBanners;