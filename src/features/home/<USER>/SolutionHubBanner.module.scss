@import "/src/styles/variables";
@import "/src/styles/mixins";

.banner {
  height: 400px;
  position: relative;
  padding: 80px 0;

  &__contents {
    color: #c2b8ff;
    text-align: center;
    position: relative;

    h6 {
      color: rgba(194, 184, 255, 0.6);
      position: absolute;
      top: 12%;
      left: 6%;
      text-align: center;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%; /* 27px */
      text-transform: uppercase;
      @include rtl-styles {
        left: auto;
        right: 6%;
      }
    }

    span {
      display: flex;
      align-items: center;
      gap: 24px;
      justify-content: center;
      h5 {
        font-family: $bricolage-font-family;
        font-optical-sizing: none;
        font-size: 56px;
        font-style: normal;
        font-weight: 800;
        line-height: 64px;
        display: inline-block;

        @include rtl-styles {
          font-family: $arabic-font-family;
        }
      }
    }

    p {
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 28px; /* 155.556% */
      letter-spacing: -0.18px;
      margin: 0;
    }
  }

  &__button {
    width: fit-content;
    cursor: pointer;
    display: flex;
    padding: 20px 40px;
    justify-content: center;
    align-items: center;
    gap: 16px;
    border-radius: 48px;
    border: 1px solid #8679d5;
    background: linear-gradient(0deg, #c2b8ff 0%, #c2b8ff 100%), #0a0118;
    font-family: $default-font-family;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px; /* 155.556% */
    letter-spacing: -0.18px;
    margin: 32px auto auto auto;

    img {
      aspect-ratio: 1/1;
    }
  }
}
