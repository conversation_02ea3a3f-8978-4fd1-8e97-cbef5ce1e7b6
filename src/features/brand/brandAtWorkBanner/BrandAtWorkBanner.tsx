import styles from "./BrandAtworkBanner.module.scss";
import { cleverTapService } from "@features/common/clevertap.services";
import useAppRouter from "@features/common/router.context";
import { useAppSelector } from "@redux/hooks";
import { getTokenInfo } from "@features/common/commonSlice";
import { PAGEURLS, USER_TYPES } from "@constants/common";
import getConfig from "next/config";
import { useEffect, useState } from "react";
import { supportWebP } from "@utils/detectWebp";

const {
  publicRuntimeConfig: { imageBaseUrl, groupGiftUrl, authRedirectUrl },
} = getConfig();

interface BrandAtWorkBanner {
  promotionData: any;
  brandCode?: string;
  brandName?: string;
}

const BrandAtWorkBanner = ({
  promotionData,
  brandCode,
  brandName,
}: BrandAtWorkBanner) => {
  const {
    router,
    state: { locale, activeRegion },
  } = useAppRouter();

  const tokenInfo = useAppSelector(getTokenInfo);
  const [webpSupported, setWebpSupported] = useState<boolean>(false);

  const handleClick = ($event: any, bannerRedirectUrl: string) => {
    $event.preventDefault();
    const data = {
      store: activeRegion?.node.country.code,
      productName: brandName || "",
      productId: brandCode || "",
      userType: tokenInfo?.AccessToken
        ? USER_TYPES.LOGGED_IN
        : tokenInfo?.isGuestUser
        ? USER_TYPES.GUEST
        : "",
    };
    cleverTapService.pushClickedOnGGBanner(data);

    const redirectUrl = bannerRedirectUrl?.startsWith("https://")
      ? bannerRedirectUrl
      : groupGiftUrl;

    const isRedirectUrlGroupGift = redirectUrl?.includes(groupGiftUrl);

    if (!tokenInfo?.isUserSignedIn) {
      router.push(
        {
          pathname: `${authRedirectUrl}/${locale}${PAGEURLS.LOGIN}/`,
          query: {
            rdir: redirectUrl,
            store: router?.locale,
            guest: isRedirectUrlGroupGift ? true : false, // ##. guest - true indicates, hide guest in auth & false indicates to show
          },
        },
        undefined,
        { locale }
      );
    } else {
      router.push(redirectUrl);
    }
  };

  useEffect(() => {
    supportWebP().then((result: any) => setWebpSupported(result));
  }, []);

  return (
    <div className={styles["brand-gg-banner"]}>
      {promotionData?.map((item: any) => (
        <div
          key={item.id}
          className={`${styles["brand-gg-banner__gg-content"]} ${styles["brand-gg-banner__gg-content-aw"]}`}
        >
          <div
            className={styles["brand-gg-banner__gg-content--img-container-aw"]}
          >
            <img
              src={
                webpSupported && item.bannerImageWebp
                  ? item.bannerImageWebp
                  : item.bannerImage
              }
            />
          </div>
          <div
            className={`${styles["brand-gg-banner__gg-content--data-container-aw"]}`}
          >
            <span
              className={
                styles["brand-gg-banner__gg-content--data-container__title"]
              }
              title={item.bannerTitle}
            >
              {item.bannerTitle}
              <img
                src={`${imageBaseUrl}/icons/back-circle.svg`}
                alt="icon"
                height={32}
                width={32}
                onClick={(e) => handleClick(e, item?.bannerRedirectUrl || "")}
                style={{ cursor: "pointer" }}
              />
            </span>
            <div
              dangerouslySetInnerHTML={{ __html: item.bannerDescription }}
              className={
                styles[
                  "brand-gg-banner__gg-content--data-container__description-aw"
                ]
              }
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default BrandAtWorkBanner;
