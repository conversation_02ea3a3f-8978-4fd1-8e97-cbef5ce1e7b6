@import "/src/styles/variables";
@import "/src/styles/mixins";

.brand-gg-banner {
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
  margin-top: 32px;

  &__gg-content {
    display: flex;
    align-items: center;
    background-color: $grey-bg;
    border-radius: 12px;

    &-aw {
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 24px;
    }

    &--img-container-gg {
      margin: 17px 20px 0px 20px;
      width: 108px;
      height: 117px;
      img {
        display: block;
        width: 100%;
        height: 100%;
        flex-grow: 0;
        border-radius: 10px 10px 0px 0px;
      }
    }

    &--img-container-aw {
      min-width: 117px;
      height: 48px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    &--data-container {
      flex: 1;
      flex-grow: 4;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      padding: 18px 16px 0px 0px;

      @include rtl-styles {
        padding: 18px 0px 0px 16px;
      }

      &__title {
        color: $dark-purple;
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: 32px;
        font-family: $bricolage-font-family;
        font-optical-sizing: none;
        align-items: center;

        display: flex;
        justify-content: space-between;
        img {
          @include rtl-rotate;
          min-width: 32px;
        }

        @include rtl-styles {
          font-family: $arabic-font-family;
        }
      }

      &__description-gg {
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        letter-spacing: -0.16px;
        color: $dark-purple;
        word-break: break-word;
        margin: 10px 0;

        @include rtl-styles {
          margin: 10px 0;
        }
      }

      &__description-aw {
        color: #868785;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px; /* 128.571% */
        letter-spacing: -0.14px;
        margin-top: 8px;
      }
    }
  }

  &__gg-link-container {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    border: solid 2px $grey-bg;
    background-color: #fff;
    border-radius: 0px 0px 12px 12px;

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      cursor: default;
    }

    .cursor {
      cursor: pointer !important;
    }

    a {
      // width: 180px;
      // height: 11px;
      flex-grow: 0;
      font-family: Poppins;
      font-size: 16px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: left;
      color: $barney-purple;
    }
  }
  .vector {
    height: 16px;
    width: 16px;
    img {
      padding: 2px !important;
      @include rtl-rotate;
    }
  }
}
