import { BANNER_TYPE, SLIDER_TYPE } from "@constants/common";
import { BLOG_QUERY } from "@features/home/<USER>/blog.query";
import { PRESSROOM_QUERY } from "@features/home/<USER>/featuredIn.query";
import { HAPPY_CARD_QUERY } from "@features/home/<USER>/happyCard.query";
import { HOWTOUSE_QUERY, QUICKVIEW_QUERY } from "@features/home/<USER>";
import { HOME_DOWNLOAD_APP_QUERY } from "@features/home/<USER>/homeDownloadApp.query";
import { PROMOTION_QUERY } from "@features/home/<USER>/promotion.query";
import { PROMOTIONAL_BANNER_QUERY } from "@features/home/<USER>/promotionalBanner.query";
import { REWARD_BANNERS_QUERY } from "@features/home/<USER>/rewardsBusiness.query";
import { TESTIMONIAL_QUERY } from "@features/home/<USER>/testimonial.query";
import {
  MajorHeaderData,
  MinorHeaderData,
  RewardsItemInterface,
  SideBannerItems,
  SiteConfigData,
  StoresData,
  PromotionData,
  PressRoomInterface,
  FooterInterface,
  BlogsInterface,
  DownloadAppInterface,
  SignatureInterface,
  Testimonials,
  BannerInterface,
  SliderInterface,
  QuickviewInterface,
  CommonDataInterface,
} from "@interfaces/common.inteface";
import DateTime from "@utils/dateTime";
import { BANNER_QUERY } from "@features/home/<USER>/banner.query";
import { PERSONALIZATION_BANNER_QUERY } from "@features/home/<USER>/personalizationBanner.query";

export const MINOR_HEADER_DATA: MinorHeaderData = {
  data: {
    headers: {
      edges: [
        {
          node: {
            backgroundImage: "/images/header-strip.png",
            headerType: {
              code: "MINOR",
            },
            tagLine: "Your gift card store",
          },
        },
      ],
    },
  },
};

export const MAJOR_HEADER_DATA: MajorHeaderData = {
  data: {
    headers: {
      edges: [
        {
          node: {
            backgroundImage: "/images/logo-yougotagift.jpg",
            tagLine: "tagline",
            eGiftCards_SeoName: "most-popular",
            gamingCards_SeoName: "game",
            headerType: {
              code: "MAJOR",
            },
            logo: "/images/logo-yougotagift",
          },
        },
      ],
    },
  },
};

export const SITE_CONFIG_DATA: SiteConfigData = {
  data: {
    siteConfigs: {
      edges: [
        {
          node: {
            defaultCountry: "ae",
            defaultLanguage: "en",
            defaultStoreCode: "STAE",
            email: "<EMAIL>",
            clevertapAccountId: "cleverTapId",
            chatKey: "zendeskAPIKey",
            chatEnabled: false,
            homepageSiteMeta: {
              id: "123456",
              title: "buy e-Gifts",
              urlPattern: "/",
              description: "hello world",
              keywords: "buy now,gift-cards,hello",
            },
            captchaConfig: [{
              hasCaptchaEnabled: true,
              actionName: "product-feedback"
            }
            ],
            recaptchaSiteKey: "recaptchaSiteKey",
            languages: ["ar", "en"],
            helpDesk: {
              edges: [
                {
                  node: {
                    phone: "1365458",
                    mobile: "1365458",
                    timing: "Saturday-Sunday 9.30am-10pm",
                    country: {
                      code: "AE",
                      name: "UAE",
                      flagImage: "/images/uae-flag.png",
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  },
};

export const STORES_DATA: StoresData = {
  data: {
    stores: {
      edges: [
        {
          node: {
            code: "STKW",
            name: "Kuwait",
            timezone: "Asia/Kuwait",
            country: {
              code: "KW",
              flagImage: "/images/uae-flag.png",
              codeThreeLetter: "KWT",
              languages: {
                edges: [
                  {
                    node: {
                      name: "Arabic",
                    },
                  },
                  {
                    node: {
                      name: "English",
                    },
                  },
                ],
              },
            },
          },
        },
        {
          node: {
            code: "STIN",
            name: "India",
            timezone: "Asia/Kolkatta",
            country: {
              code: "IN",
              flagImage: "/images/sa-flag.png",
              codeThreeLetter: "IND",
              languages: {
                edges: [
                  {
                    node: {
                      name: "English",
                    },
                  },
                ],
              },
            },
          },
        },
      ],
    },
  },
};

/**
 * Mock data for the promotion banner popup
 */
export const PROMOTION_DATA: PromotionData = {
  data: {
    promotion: {
      backgroundColor: "#f09393",
      url: "/page",
      ctaText: "Learn more",
      title: "main title",
      subTitle: "<p>sub title</p>",
      isPopup: true,
      description: "<p data-testid='testDesc'>description</p>",
      image: "logo.png",
      __typename: "",
    },
  },
};

export const PROMOTION_DATA_REQUEST_MOCKS = [
  {
    request: {
      query: PROMOTION_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: "EN",
        date: DateTime.today(),
      },
    },
    result: PROMOTION_DATA,
  },
];

/**
 * Mock data for the promotion banner popup
 */
export const PROMOTION_DATA_FALSY: PromotionData = {
  data: {
    promotion: {
      backgroundColor: "#f09393",
      url: "/page",
      ctaText: "",
      title: "main title",
      subTitle: "<p>sub title</p>",
      isPopup: false,
      description: "<p>description</p>",
      image: "logo.png",
      __typename: "",
    },
  },
};

export const PROMOTION_DATA_FALSY_REQUEST_MOCKS = [
  {
    request: {
      query: PROMOTION_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: "EN",
        date: DateTime.today(),
      },
    },
    result: PROMOTION_DATA_FALSY,
  },
];

export const BANNER_DATA: BannerInterface = {
  data: {
    banners: [
      {
        image: "/banner-1.png",
        name: "test1",
        url: "/banner1",
        borderColor: "red",
        bannerCategory: {
          slidingTimeInterval: 3000,
          isSliding: true,
          code: BANNER_TYPE.MAIN,
          floatingType: null,
        },
        bannerDescription: "Test1",
        isPopup: false,
      },
      {
        image: "/banner-2.png",
        name: "test2",
        url: "/banner2",
        borderColor: "red",
        bannerCategory: {
          slidingTimeInterval: 3000,
          isSliding: true,
          code: BANNER_TYPE.MAIN,
          floatingType: null,
        },
        bannerDescription: "Test2",
        isPopup: true,
      },
      {
        image: "/banner-for-business.png",
        name: "test",
        url: "/",
        borderColor: "red",
        bannerCategory: {
          slidingTimeInterval: null,
          isSliding: false,
          code: BANNER_TYPE.SIDE,
          floatingType: null,
        },
        bannerDescription: "",
        isPopup: false,
      },
      {
        image: "reward-business-en.png",
        name: "test",
        borderColor: "red",
        url: "/",
        bannerCategory: {
          slidingTimeInterval: null,
          isSliding: false,
          code: BANNER_TYPE.REWARDS,
          floatingType: null,
        },
        bannerDescription: "",
        isPopup: false,
      },
      {
        name: "Blue salon egift cards",
        bannerDescription: "test",
        isPopup: false,
        image: "/promotional-banner-1.png",
        url: "/",
        borderColor: "red",
        bannerCategory: {
          slidingTimeInterval: null,
          isSliding: false,
          code: BANNER_TYPE.PROMOTIONAL,
          floatingType: "VERTICAL",
        },
      },
      {
        name: "kunooz egift cards",
        image: "/promotional-banner-2.png",
        bannerDescription: "test",
        isPopup: false,
        url: "/",
        borderColor: "red",
        bannerCategory: {
          slidingTimeInterval: null,
          isSliding: false,
          code: BANNER_TYPE.PROMOTIONAL,
          floatingType: "VERTICAL",
        },
      },
      {
        name: "Horizontal Blue salon egift cards",
        image: "/promotional-banner-3.png",
        bannerDescription: "test",
        isPopup: false,
        url: "/",
        borderColor: "red",
        bannerCategory: {
          slidingTimeInterval: null,
          isSliding: false,
          code: BANNER_TYPE.PROMOTIONAL,
          floatingType: "HORIZONTAL",
        },
      },
    ],
  },
};

export const BANNER_DATA_REQUEST_MOCKS = [
  {
    request: {
      query: BANNER_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: "EN",
        date: DateTime.today(),
      },
    },
    result: BANNER_DATA,
  },
];

export const PROMOTIONAL_BANNER_DATA_REQUEST_MOCKS = [
  {
    request: {
      query: PROMOTIONAL_BANNER_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: "EN",
        date: DateTime.today(),
        bannerCategory_Code: BANNER_TYPE.PROMOTIONAL,
      },
    },
    result: BANNER_DATA,
  },
];

export const SIDE_BANNER_DATA: SideBannerItems = {
  image: "/banner-for-business.png",
  url: "/",
};

export const REWARD_DATA_WEB_REQUEST_MOCKS = [
  {
    request: {
      query: REWARD_BANNERS_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: undefined,
        date: DateTime.today(),
        bannerCategory_Code: BANNER_TYPE.REWARDS,
      },
    },
    result: BANNER_DATA,
  },
];
export const REWARD_DATA_TAB_REQUEST_MOCKS = [
  {
    request: {
      query: REWARD_BANNERS_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: undefined,
        date: DateTime.today(),
        bannerCategory_Code: BANNER_TYPE.REWARDS,
      },
    },
    result: BANNER_DATA,
  },
];

export const REWARDS_DATA: RewardsItemInterface = {
  image: "reward-business-en.png",
  bannerCategory: {
    code: BANNER_TYPE.REWARDS,
  },
  url: "/",
};

export const REWARDS_TABLET_DATA: RewardsItemInterface = {
  image: "reward-business-en-tablet.png",
  bannerCategory: {
    code: BANNER_TYPE.REWARDS,
  },
  url: "/",
};

export const FEATURED_DATA: PressRoomInterface = {
  data: {
    pressRooms: [
      {
        name: "Arab News",
        pressUrl: "https://www.arabnews.com/node/1487556/corporate-news",
        pressLogo: "press-room-1.png",
        code: "arab-news",
        orderNumber: 10,
      },
      {
        name: "Alarabiya",
        pressUrl:
          "https://english.alarabiya.net/business/technology/2014/03/15/YouGotaGift-taps-into-Mideast-online-money-making",
        pressLogo: "press-room-1.png",
        code: "alarabiya",
        orderNumber: 9,
      },
    ],
  },
};

export const FEATURED_DATA_REQUEST_MOCKS = [
  {
    request: {
      query: PRESSROOM_QUERY,
    },
    result: FEATURED_DATA,
  },
];

export const FOOTER_DATA: FooterInterface = {
  data: {
    footer: {
      appBanner: [
        {
          itemImage: "/apple-badge.png",
          itemName: "apple badge",
          itemUrl: "/",
        },
        {
          itemImage: "/android-badge.png",
          itemName: "android badge",
          itemUrl: "/",
        },
      ],
      contact: {
        email: "<EMAIL>",
        helpDesk: {
          edges: [
            {
              node: {
                country: {
                  code: "SA",
                  flagImage: "/sa-flag.png",
                  name: "Saudi Arabia",
                },
                mobile: "+966920000175",
                phone: "",
                timing: "Everyday 9am-9pm UAE",
              },
            },
            {
              node: {
                country: {
                  code: "AE",
                  flagImage: "/uae-flag.png",
                  name: "UAE",
                },
                mobile: "+9714418473",
                phone: "",
                timing: "Everyday 9am-10pm UAE",
              },
            },
          ],
        },
      },
      copyrightNotice: "@2021 YouGotaGift.com Ltd.All rights reserved.",
      footerStores: [
        {
          storeLink: "/en-ae",
          store: {
            country: {
              name: "UAE",
            },
            name: "UAE",
          },
        },
        {
          storeLink: "/en-ae",
          store: {
            country: {
              name: "Saudi Arabia",
            },
            name: "Saudi Arabia",
          },
        },
        {
          storeLink: "/en-ae",
          store: {
            country: {
              name: "Oman",
            },
            name: "Oman",
          },
        },
      ],
      logo: "/footer-logo.png",
      paymentPartners: {
        edges: [
          {
            node: {
              code: "VISA",
              logo: "/visa-logo.png",
              name: "Visa",
            },
          },
          {
            node: {
              code: "MASTERCARD",
              logo: "/master-card-logo.png",
              name: "Master Card",
            },
          },
          {
            node: {
              code: "AMEX",
              logo: "/american-express-logo.png",
              name: "American Express",
            },
          },
          {
            node: {
              code: "PCIDSS",
              logo: "/pci-dss-logo.png",
              name: "Pci Dss",
            },
          },
        ],
      },
      subMenu: [
        {
          itemLabel: "Home",
          itemName: "Home",
          itemUrl: null,
          children: {
            edges: [
              {
                node: {
                  itemLabel: "About Us",
                  itemName: "About Us",
                  itemUrl: "/",
                  itemImage: "",
                },
              },
              {
                node: {
                  itemLabel: "Buy eGift Cards",
                  itemName: "Buy eGift Cards",
                  itemUrl: "/",
                  itemImage: "",
                },
              },
              {
                node: {
                  itemLabel: "Buy Gaming Cards",
                  itemName: "Buy Gaming Cards",
                  itemUrl: "/",
                  itemImage: "",
                },
              },
            ],
          },
        },
        {
          itemLabel: "Policy & Info",
          itemName: "Policy & Info",
          itemUrl: null,
          children: {
            edges: [
              {
                node: {
                  itemLabel: "Privacy Policy",
                  itemName: "Privacy Policy",
                  itemUrl: "/",
                  itemImage: "",
                },
              },
              {
                node: {
                  itemLabel: "Brand Guidelines",
                  itemName: "Brand Guidelines",
                  itemUrl: "/",
                  itemImage: "",
                },
              },
              {
                node: {
                  itemLabel: "Partner with US",
                  itemName: "Partner with US",
                  itemUrl: "/",
                  itemImage: "",
                },
              },
            ],
          },
        },
        {
          itemLabel: "Our Socials",
          itemName: "Social Media Icon",
          itemUrl: null,
          children: {
            edges: [
              {
                node: {
                  itemLabel: "Linkedin",
                  itemName: "linked in",
                  itemUrl: "/",
                  itemImage: "linked-in.png",
                },
              },
              {
                node: {
                  itemLabel: "Facebook",
                  itemName: "facebook icon",
                  itemUrl: "/",
                  itemImage: "facebook-icon.png",
                },
              },
              {
                node: {
                  itemLabel: "Instagram",
                  itemName: "insta icon",
                  itemUrl: "/",
                  itemImage: "insta-icon.png",
                },
              },
            ],
          },
        },
      ],
      tagLine:
        "YouGotaGift.com is the leading Digital Gift Card Company in the Middle East.",
    },
  },
};

export const BLOG_DATA: BlogsInterface = {
  data: {
    blogs: {
      edges: [
        {
          node: {
            image: "/blog-img01.png",
            title: "blog 01",
            url: "/blog1",
          },
        },
        {
          node: {
            image: "/blog-img02.png",
            title: "blog 02",
            url: "/blog2",
          },
        },
      ],
    },
  },
};

export const BLOG_DATA_REQUEST_MOCKS = [
  {
    request: {
      query: BLOG_QUERY,
    },
    result: BLOG_DATA,
  },
];

export const HOME_DOWNLOAD_APP_DATA: DownloadAppInterface = {
  data: {
    downloadApp: [
      {
        appBanner: [
          {
            id: "TWVudUl0ZW1Ob2RlOjc=",
            itemImage: "/apple-badge.png",
            itemName: "apple badge",
            itemUrl: "/",
          },
          {
            id: "TWVudUl0ZW1Ob2RlOjY=",
            itemImage: "/android-badge.png",
            itemName: "android badge",
            itemUrl: "/",
          },
        ],
        bannerImage: "/banner-image.png",
        text: {
          edges: [
            {
              node: {
                id: "RG93bmxvYWRBcHBUZXh0Tm9kZTox",
                text: "Customise greeting cards",
              },
            },
            {
              node: {
                id: "RG93bmxvYWRBcHBUZXh0Tm9kZToy",
                text: "Receive upcoming Notifications",
              },
            },
          ],
        },
        title: "Buy, send and store eGift cards",
      },
    ],
  },
};

export const SIGNATURE_DATA: SignatureInterface = {
  data: {
    siteConfigs: {
      edges: [
        {
          node: {
            signatureTitle: "Signature Title",
            signatureSubtitle: "Signature Sub Title",
          },
        },
      ],
    },
  },
};

export const TESTIMONIALS_DATA: Testimonials = {
  data: {
    testimonials: {
      edges: [
        {
          node: {
            heading: "Why Our Customers Love Us",
            testimonialVideo: {
              edges: [{ node: { link: "https://youtu.be/SVbL5_MlDLs" } }],
            },
            testimonialReview: {
              edges: [
                {
                  node: {
                    customerName: "testimonialReview Title",
                    description: "testimonialReview Description",
                    happinessIndex: 5,
                    testimonialOrder: {
                      edges: [
                        {
                          node: {
                            country: { code: "SA", codeThreeLetter: "SAU" },
                            dateTime: new Date("2015-03-25"),
                            productCode: "SAYGAGRTL",
                            productName: "YouGotaGift Happy Card",
                          },
                        },
                      ],
                    },
                  },
                },
                {
                  node: {
                    customerName: "testimonialReview Title 2",
                    description: "testimonialReview Description 2",
                    happinessIndex: 2,
                    testimonialOrder: {
                      edges: [
                        {
                          node: {
                            country: { code: "BH", codeThreeLetter: "BHR" },
                            dateTime: new Date("2015-03-25"),
                            productCode: "SAYGAGRTL",
                            productName: "YouGotaGift Happy Card",
                          },
                        },
                      ],
                    },
                  },
                },
                {
                  node: {
                    customerName: "testimonialReview Title 3",
                    description: "testimonialReview Description 3",
                    happinessIndex: 1,
                    testimonialOrder: {
                      edges: [
                        {
                          node: {
                            country: { code: "AE", codeThreeLetter: "AEU" },
                            dateTime: new Date("2015-03-25"),
                            productCode: "SAYGAGRTL",
                            productName: "YouGotaGift Happy Card",
                          },
                        },
                      ],
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  },
};

export const TESTIMONIALS_DATA_REQUEST_MOCKS = [
  {
    request: {
      query: TESTIMONIAL_QUERY,
      variables: {
        country_Code: "AE",
      },
    },
    result: TESTIMONIALS_DATA,
  },
];

export const HAPPY_CARD_MOCK_DATA = {
  data: {
    happyCard: {
      title: "One Card.All Brands.",
      subTitle: "YouGotaGift Happy Card ®",
      description:
        "<p>The YouGotaGift Happy Card is a super card, redeemable at all of our partner brands in the UAE.&nbsp;</p>",
      cardImage: "",
      url: "/",
      brand: {
        brandImageData:
          "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/85d72dbe-418f-4c66-9999-10e471314d04.jpg",
      },
      redeemableBrands: {
        totalCount: 6,
        edges: [
          {
            node: {
              brand: {
                code: "MECC",
                name: "Mall of the Emirates & City Centre",
                brandImageData:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/6d44ebb9-426a-4051-9c84-39e41fbd1315.jpg",
                primaryCategory: {
                  name: "Shopping Malls",
                  id: "QnJhbmRDYXRlZ29yeU5vZGU6NDU=",
                },
              },
            },
          },
          {
            node: {
              brand: {
                code: "6TH",
                name: "6thStreet",
                brandImageData:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/14895bbb-adfc-4273-abbb-872f39be2e01.jpg",
                primaryCategory: {
                  name: "Shopping Malls",
                  id: "QnJhbmRDYXRlZ29yeU5vZGU6NDU=",
                },
              },
            },
          },
          {
            node: {
              brand: {
                code: "WAHMAL",
                name: "Al Wahda Mall",
                brandImageData:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/2559834e-2d31-4d44-a5bc-3177e226974d.jpg",
                primaryCategory: {
                  name: "Shopping Malls",
                  id: "QnJhbmRDYXRlZ29yeU5vZGU6NDU=",
                },
              },
            },
          },
          {
            node: {
              brand: {
                code: "ANGH",
                name: "Anghami",
                brandImageData:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/71df3529-3625-47d8-9d96-1bbf46b08998.jpg",
                primaryCategory: {
                  name: "Shopping Malls",
                  id: "QnJhbmRDYXRlZ29yeU5vZGU6NDU=",
                },
              },
            },
          },
          {
            node: {
              brand: {
                code: "ALDO",
                name: "Aldo | Apparel Gift Card",
                brandImageData:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/7d57af9b-fefd-4bbf-a66a-ab6b6f67f57c.jpg",
                primaryCategory: {
                  name: "Shopping Malls",
                  id: "QnJhbmRDYXRlZ29yeU5vZGU6NDU=",
                },
              },
            },
          },
          {
            node: {
              brand: {
                code: "APRL",
                name: "Apparel Gift Card",
                brandImageData:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/stores/brand/brand_images/2022/04/19/074a95f8-f12f-4d1f-baed-9cc3c876e0a3.jpg",
                primaryCategory: {
                  name: "Shopping Malls",
                  id: "QnJhbmRDYXRlZ29yeU5vZGU6NDU=",
                },
              },
            },
          },
        ],
      },
    },
  },
};

export const HAPPY_CARD_REQUEST_MOCKS = [
  {
    request: {
      query: HAPPY_CARD_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
      },
    },
    result: HAPPY_CARD_MOCK_DATA,
  },
];

export const PERSONALIZE_BANNER_REQUEST_MOCKS = [
  {
    request: {
      query: PERSONALIZATION_BANNER_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: "EN",
        date: DateTime.today(),
      },
    },
    result: {
      data: {
        banners: [
          {
            name: "personalization banner",
            image: "/personalize-banner.png",
            url: "/",
            bannerType: "STATIC",
            bannerDescription: "",
            bannerContent: "",
          },
        ],
      },
    },
  },
];

// #. NEWS configuration data
export const NEWS_CONFIG_DATA = {
  data: {
    newsletterConfiguration: {
      title: "Subscribe to Our",
      subTitle: "For updates, offers and other cool things we might be up to",
      buttonName: "Subscribe",
    },
  },
};

export const NEWS_SUBSCRIPTION_SUCCESS = {
  data: {
    emailSubscription: {
      subscription: {
        emailAddress: "<EMAIL>",
      },
    },
  },
};

export const NEWS_SUBSCRIPTION_FAILED = {
  errors: [
    {
      message: "Email subscription with this Email address already exists.",
    },
  ],
};

export const DOWNLOAD_APP_MOCKS = [
  {
    request: {
      query: HOME_DOWNLOAD_APP_QUERY,
      variables: {
        platformType_Code: "WEB",
        country_Code: "AE",
        language_Code: "EN",
        date: DateTime.today(),
      },
    },
    result: {
      data: {
        downloadApp: [
          {
            title: "Buy, send and store eGift cards",
            bannerImage:
              "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/configurations/download_app/image/2022/04/26/b7a74e59-912d-4ae3-b1ad-a34626570afe.png",
            appBanner: [
              {
                id: "TWVudUl0ZW1Ob2RlOjc=",
                itemImage:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/configurations/menu/menuImage/2022/04/19/41dc809a-e6bc-4f32-9961-7e846c4a7af2.png",
                itemLabel: null,
                itemName: "apple badge",
                itemUrl: "/",
              },
              {
                id: "TWVudUl0ZW1Ob2RlOjY=",
                itemImage:
                  "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/configurations/menu/menuImage/2022/04/19/95f7c361-2b39-40c5-827c-ceae0696898e.png",
                itemLabel: null,
                itemName: "android badge",
                itemUrl: "/",
              },
            ],
            text: {
              edges: [
                {
                  node: {
                    id: "RG93bmxvYWRBcHBUZXh0Tm9kZTox",
                    text: "Customise greeting cards",
                  },
                },
                {
                  node: {
                    id: "RG93bmxvYWRBcHBUZXh0Tm9kZToy",
                    text: "Receive upcoming Notifications",
                  },
                },
              ],
            },
          },
        ],
      },
    },
  },
];

export const cardSliderBrandMockData: SliderInterface = {
  ctaText: "See More",
  ctaTextAr: "See More",
  enableCta: true,
  heading: "Main Heading",
  slug: "brand",
  isScrollable: true,
  redemptionBadges: [{ type: "online", label: "Online" }],
  sliderBrand: {
    edges: [
      {
        node: {
          brandData: {
            brand: {
              brandImageData: "/brand-image.jpg",
              logoImageData: "/logo.png",
              buyForYourself: true,
              renderPreviewPage: false,
              isGeneric: true,
              siteMeta: {
                id: "123456",
                title: "hello",
                urlPattern: "/",
                description: "hello world",
                keywords: "buy now,gift-cards,hello",
                noIndex: false
              },
              company: {
                code: "Company code",
                name: "Company name",
              },
              crossSellBrands: {
                edges: [
                  {
                    node: {
                      brand: {
                        brandImageData: "Brand Image Data",
                        code: "Brand Code",
                        name: "Brand name",
                        primaryCategory: {
                          code: "Primary Category Code",
                          name: "Primary name",
                        },
                      },
                    },
                  },
                ],
              },
              denominationRange: "AED 123",
              description: "Test Description",
              expiry: "Expiry",
              id: "Brand Id",
              imageGallery: {
                edges: [
                  {
                    node: {
                      image: "/imagegallery-image-1.jpg",
                    },
                  },
                  {
                    node: {
                      image: "/imagegallery-image-1.jpg",
                    },
                  },
                ],
              },
              label: "Label",
              name: "name",
              code: "code",
              slug: "brand",
              primaryCategory: {
                name: "Primary category name",
              },
              redemptionDetails: "Redemption details",
              redemptionType: "redemtion type",
              store: {
                country: {
                  flagImage: "/flag-image.jpg",
                },
              },
              website: "http://website.com",
            },
            orderNumber: "1",
          },
        },
      },
    ],
  },
  sliderCategories: {
    edges: [],
  },
  sliderType: SLIDER_TYPE.BRAND,
  category: {
    seoName: "most-popular",
  },
};
export const cardSliderCategoryMockData: SliderInterface = {
  ctaText: "See More",
  ctaTextAr: "See More",
  enableCta: true,
  heading: "Main Heading",
  slug: "brand",
  isScrollable: true,
  redemptionBadges: [{ type: "online", label: "Online" }],
  sliderBrand: {
    edges: [],
  },
  category: {
    seoName: "most-popular",
  },
  sliderCategories: {
    edges: [
      {
        node: {
          code: "0937FA6894AE4FA3BCBD",
          iconImage: "/icon.png",
          name: "Category name",
          title: "Send eGift Cards from home",
          seoName: "home-garden",
          tagType: "category",
        },
      },
    ],
  },
  sliderType: SLIDER_TYPE.CATEGORY,
};

export const crossSellBrands = {
  totalCount: 2,
  edges: [
    {
      node: {
        brand: {
          brandImageData: "/images/brand-image-data.jpg",
          code: "Brand Code",
          name: "Brand name",
          id: "id1",
          slug: "slug-one",
          primaryCategory: {
            code: "Primary Category Code",
            name: "Primary name",
          },
        },
      },
    },
    {
      node: {
        brand: {
          brandImageData: "/images/brand-image-data2.jpg",
          code: "Brand Code 2",
          name: "Brand name 2",
          id: "id2",
          slug: "slug-two",
          primaryCategory: {
            code: "Primary Category Code 2",
            name: "Primary name 2",
          },
        },
      },
    },
  ],
};

export const QuickviewData: QuickviewInterface = {
  brand: {
    redeemAt: true,
    primaryCategory: {
      code: "primary code",
      name: "Noon",
    },
    brandImageData: "/images/brandImageData.png",
    imageGallery: {
      edges: [
        {
          node: {
            image: "/images/brandImageGallery1.png",
            caption: "Gallery image1",
          },
        },
        {
          node: {
            image: "/images/brandImageGallery2.png",
            caption: "Gallery image2",
          },
        },
        {
          node: {
            image: "/images/brandImageGallery3.png",
            caption: "Gallery image3",
          },
        },
      ],
    },
    code: "code",
    name: "brand name",
    logoImageData: "/images/brandLogo.png",
    company: {
      code: "company code",
      name: "company name",
    },
    denominationRange: "AED 10 - AED 1,000",
    storeLocations: {
      edges: [],
    },
    // quickViewData: [],
    description: "description",
    redemptionType: "redemption type",
    redemptionDetails: ["redemption details"],
    store: {
      country: {
        name: "store country name",
        flagImage: "/image/country-flag.png",
      },
    },
    expiry: "6 months",
    label: "online friendly",
  },
  crossSellBrandConfig: [
    {
      crossSellBrands: crossSellBrands,
    },
  ],
};

export const QUICKVIEWDATA_MOCK = [
  {
    request: {
      query: QUICKVIEW_QUERY,
      variables: {
        id: "",
      },
    },
    result: {
      data: QuickviewData,
    },
  },
];

export const HOWTOUSE_DATA_MOCK = [
  {
    request: {
      query: HOWTOUSE_QUERY,
    },
    result: {
      data: {
        howToUses: {
          edges: [
            {
              node: {
                title: "How to send a Gift Card",
                howToUseBanner: {
                  edges: [
                    {
                      node: {
                        id: "SG93VG9Vc2VCYW5uZXJOb2RlOjE=",
                        bannerImage:
                          "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/configurations/how_to_use/2022/05/09/8ada540d-236a-4247-bec5-efcae432f3a0.png",
                      },
                    },
                    {
                      node: {
                        id: "SG93VG9Vc2VCYW5uZXJOb2RlOjI=",
                        bannerImage:
                          "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/configurations/how_to_use/2022/05/09/2c74f871-81b5-4305-a685-282635f6010b.png",
                      },
                    },
                    {
                      node: {
                        id: "SG93VG9Vc2VCYW5uZXJOb2RlOjM=",
                        bannerImage:
                          "https://ygag-ecom-qa-tf.s3.us-east-2.amazonaws.com/media/images/configurations/how_to_use/2022/05/09/b61d6b98-f56f-4497-b6de-3cf1ab2d31ed.png",
                      },
                    },
                  ],
                },
              },
            },
          ],
        },
      },
    },
  },
];

export const PRODUCT_FEEDBACK_DATA = {
  productFeedbackBox: {
    productFeedbackBox: {
      emailAddress: "<EMAIL>",
      brandName: "test",
    },
  },
};

export const BRAND_OCCASIONS_DATA = {
  data: {
    occasions: {
      edges: [
        {
          node: {
            name: "Anniversary",
            code: "ANN",
            illustrationCount: 15,
            gifIllustrationCount: 0,
            isActive: true,
          },
        },
        {
          node: {
            name: "Baby",
            code: "BAB",
            illustrationCount: 7,
            gifIllustrationCount: 0,
            isActive: true,
          },
        },
      ],
    },
  },
};

export const BRAND_GIFILLUSTRATION_DATA = {
  data: {
    gifIllustrations: {
      edges: [
        {
          node: {
            occasion: {
              name: "Birthday",
            },
            gifFile: "http://hello-world.gif",
          },
        },
        {
          node: {
            occasion: {
              name: "Birthday",
            },
            gifFile: "http://jish-test.gif",
          },
        },
      ],
    },
  },
};

export const BRAND_GIF_SELECT = [
  { value: "standard" },
  {
    value: "animation",
  },
];
export const BRAND_LANGUAGE_SELECT = [
  { value: "en" },
  {
    value: "ar",
  },
];

export const BRAND_ILLUSTRATION_DATA = {
  data: {
    illustrations: {
      edges: [
        {
          node: {
            occasion: {
              name: "Anniversary",
            },
            iconImage: "http://virat.png",
            cardImage: "http://kohli.png",
          },
        },
        {
          node: {
            occasion: {
              name: "Anniversary",
            },
            iconImage: "sins.png",
            cardImage: "depp.png",
          },
        },
      ],
    },
  },
};

export const COMMON_MOCK_DATA: CommonDataInterface = {
  data: {
    headers: {
      edges: [
        {
          node: {
            backgroundImage: "/images/logo-yougotagift.jpg",
            tagLine: "tagline",
            eGiftCards_SeoName: "most-popular",
            gamingCards_SeoName: "game",
            headerType: {
              code: "MAJOR",
            },
            logo: "/images/logo-yougotagift",
          },
        },
        {
          node: {
            backgroundImage: "/images/header-strip.png",
            headerType: {
              code: "MINOR",
            },
            tagLine: "Your gift card store",
          },
        },
      ],
    },
    footer: {
      appBanner: [
        {
          itemImage: "/apple-badge.png",
          itemName: "apple badge",
          itemUrl: "/",
        },
        {
          itemImage: "/android-badge.png",
          itemName: "android badge",
          itemUrl: "/",
        },
      ],
      contact: {
        email: "<EMAIL>",
        helpDesk: {
          edges: [
            {
              node: {
                country: {
                  code: "SA",
                  flagImage: "/sa-flag.png",
                  name: "Saudi Arabia",
                },
                mobile: "+966920000175",
                phone: "",
                timing: "Everyday 9am-9pm UAE",
              },
            },
            {
              node: {
                country: {
                  code: "AE",
                  flagImage: "/uae-flag.png",
                  name: "UAE",
                },
                mobile: "+9714418473",
                phone: "",
                timing: "Everyday 9am-10pm UAE",
              },
            },
          ],
        },
      },
      copyrightNotice: "@2021 YouGotaGift.com Ltd.All rights reserved.",
      footerStores: [
        {
          storeLink: "/en-ae",
          store: {
            country: {
              name: "UAE",
            },
            name: "UAE",
          },
        },
        {
          storeLink: "/en-ae",
          store: {
            country: {
              name: "Saudi Arabia",
            },
            name: "Saudi Arabia",
          },
        },
        {
          storeLink: "/en-ae",
          store: {
            country: {
              name: "Oman",
            },
            name: "Oman",
          },
        },
      ],
      logo: "/footer-logo.png",
      paymentPartners: {
        edges: [
          {
            node: {
              code: "VISA",
              logo: "/visa-logo.png",
              name: "Visa",
            },
          },
          {
            node: {
              code: "MASTERCARD",
              logo: "/master-card-logo.png",
              name: "Master Card",
            },
          },
          {
            node: {
              code: "AMEX",
              logo: "/american-express-logo.png",
              name: "American Express",
            },
          },
          {
            node: {
              code: "PCIDSS",
              logo: "/pci-dss-logo.png",
              name: "Pci Dss",
            },
          },
        ],
      },
      subMenu: [
        {
          itemLabel: "Home",
          itemName: "Home",
          itemUrl: null,
          children: {
            edges: [
              {
                node: {
                  itemLabel: "About Us",
                  itemName: "About Us",
                  itemUrl: "/",
                },
              },
              {
                node: {
                  itemLabel: "Buy eGift Cards",
                  itemName: "Buy eGift Cards",
                  itemUrl: "/",
                },
              },
              {
                node: {
                  itemLabel: "Buy Gaming Cards",
                  itemName: "Buy Gaming Cards",
                  itemUrl: "/",
                },
              },
            ],
          },
        },
        {
          itemLabel: "Policy & Info",
          itemName: "Policy & Info",
          itemUrl: null,
          children: {
            edges: [
              {
                node: {
                  itemLabel: "Privacy Policy",
                  itemName: "Privacy Policy",
                  itemUrl: "/",
                },
              },
              {
                node: {
                  itemLabel: "Brand Guidelines",
                  itemName: "Brand Guidelines",
                  itemUrl: "/",
                },
              },
              {
                node: {
                  itemLabel: "Partner with US",
                  itemName: "Partner with US",
                  itemUrl: "/",
                },
              },
            ],
          },
        },
      ],
      tagLine:
        "YouGotaGift.com is the leading Digital Gift Card Company in the Middle East.",
    },
    siteConfigs: {
      edges: [
        {
          node: {
            atworkEnabled: true,
            defaultCountry: "AE",
            defaultLanguage: "en",
            defaultStoreCode: "STAE",
            email: "<EMAIL>",
            signatureSubtitle: "",
            recaptchaSiteKey: "recaptchaSiteKey",
            languages: ["ar", "en"],
            signatureTitle: "",
            clevertapAccountId: "cleverTapId",
            chatKey: "zendeskAPIKey",
            chatEnabled: false,
            solutionHubEnabled: false,
            homepageSiteMeta: {
              id: "123456",
              title: "buy e-Gifts",
              urlPattern: "/",
              description: "hello world",
              keywords: "buy now,gift-cards,hello",
            },
            captchaConfig: [{
              hasCaptchaEnabled: true,
              actionName: "product-feedback"
            }
            ],
            helpDesk: {
              edges: [
                {
                  node: {
                    phone: "1365458",
                    mobile: "1365458",
                    timing: "Saturday-Sunday 9.30am-10pm",
                    country: {
                      code: "AE",
                      name: "UAE",
                      flagImage: "/images/uae-flag.png",
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
    stores: {
      edges: [
        {
          node: {
            code: "STKW",
            name: "Kuwait",
            timezone: "Asia/Kuwait",
            country: {
              code: "KW",
              flagImage: "/images/uae-flag.png",
              codeThreeLetter: "KWT",
              languages: {
                edges: [
                  {
                    node: {
                      name: "Arabic",
                    },
                  },
                  {
                    node: {
                      name: "English",
                    },
                  },
                ],
              },
            },
          },
        },
        {
          node: {
            code: "STIN",
            name: "India",
            timezone: "Asia/Kolkatta",
            country: {
              code: "IN",
              flagImage: "/images/sa-flag.png",
              codeThreeLetter: "IND",
              languages: {
                edges: [
                  {
                    node: {
                      name: "English",
                    },
                  },
                ],
              },
            },
          },
        },
      ],
    },
  },
};
