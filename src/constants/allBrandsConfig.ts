/**
 * @enum BRAND_FILTER_TYPE
 * @description brand filter types
 * 3 types of filters available category & occasion
 */

export enum BRAND_FILTER_TYPE {
  CATEGORY = "categories",
  OCCASION = "occasions",
  SEARCH = "search",
  WHO_IS_IT_FOR = "whoisitfor"
}

// #. To the pagination setting
export const ALL_BRAND_ITEMS_PER_VIEW = 24;

export enum BRAND_SORT_BY {
  POPULAR = "popularity",
  RECENT = "recent",
  NAME = "name",
}

export const BRAND_SORT_COOKIE_KEY = "YGAG_CAT_SORT";
export const BRAND_FILTER_SLIDER_COOKIE_KEY = "YGAG_FIL_SLIDER";

export const MOST_POPULAR_CATEGORY_SEO_NAME = "most-popular";
