// Platform Types
export enum PLATFORM_TYPE {
  WEB = "WEB",
  TABLET = "TABLET",
  MOBILE = "MOBILE",
}

// Header Types
export enum HEADER_TYPE {
  MAJOR = "MAJOR",
  MINOR = "MINOR",
}

// Banner Types
export enum BANNER_TYPE {
  MAIN = "MAIN",
  SIDE = "SIDE",
  REWARDS = "REWARDS",
  PROMOTIONAL = "PROMOTIONAL",
}

// Slider Types
export enum SLIDER_TYPE {
  CATEGORY = "CATEGORY_SLIDER",
  BRAND = "BRAND_SLIDER",
}

// Widget Types
export enum WIDGET_TYPE {
  BLOG = "BLOG",
  REWARDS_BANNER = "REWARDS_BANNER",
  PROMOTIONAL_BANNER = "PROMOTIONAL_BANNER",
  DOWNLOAD_APP = "DOWNLOAD_APP",
}

// pages types
export enum PAGES {
  HOME = "Home",
  ALL_BRANDS = "All Brands",
  CART = "Cart",
  BRAND = "Brand",
}

// Page URL'S
export enum PAGEURLS {
  HOME = "/",
  LOGIN = "/login",
  ALL_BRANDS = "/brands",
  ALL_BRANDS_SEARCH = "/all",
  APP_DOWNLOAD = "/app-download",
  BACK_SOON = "/back-soon",
  CART = "/cart/",
  ABOUT_US = "/about-us",
  PRIVACY_POLICY = "/privacy-policy",
  TERMS = "/terms-of-use",
  BRAND = "/brands",
  UNSUPPORTED_COUNTRY = "/unsupported-country",
  PAYMENT_SUCCESS = "/payment/success",
  PAYMENT_FAIL = "/payment/error",
  GIFT_OPEN_PUBLIC = "/gifts/greetings",
  SUSPICIOUS_ACTIVITY = "/suspicious-activity",
  DATA_DELETION_INSTRUCTIONS = "/data-deletion-instructions",
  MEDIA_CENTER = "/media-center",
  PROFILE = "/account/profile",
  CATERGORIES = "/categories",
  GROUP_GIFTING = "/group-gifting/",
  OFFERS = "/offers/",
  WORK = "atwork/",
  START_GG = "start-group-gift",
  ACCOUNT = "/account/profile",
  ORDERS = "/account/orders",
  GAMING_GIFT_CARD = "/gaming-gift-card"
}

// Signin Signup screens
export enum SIGNIN_SIGNUP_SCREENS {
  SIGNIN_MOBILE = "signin-mobile",
  OTP_VERIFICATION = "otp-verification",
  ADD_INFO = "add-info",
  ADD_INFO_WITH_EMAIL = "add-info-with-email",
  SIGNIN_EMAIL = "signin-email",
  CHANGE_PASSWORD = "change-password",
  ENTER_DETAILS = "enter-details",
  FORGOT_PASSWORD = "forgot-password",
}

// Cognito oauth scopes
export const COGNITO_OAUTH_SCOPES = [
  "profile",
  "email",
  "openid",
  "https://www.googleapis.com/auth/user.gender.read",
  "https://www.googleapis.com/auth/user.phonenumbers.read",
  "aws.cognito.signin.user.admin",
];

// Cognito static configurations
export enum COGNITO_CONFIG {
  USER_PASSWORD_AUTH_TYPE = "USER_PASSWORD_AUTH",
  CUSTOM_AUTH_TYPE = "CUSTOM_AUTH",
  RESPONSE_TYPE = "code",
  RESPONSE_SEPARATOR = "1*#*1",
}

export enum COGNITO_USER_POOL_FIELDS {
  NAME = "name",
  EMAIL = "email",
  PHONE = "phone_number",
  PHONE_VERIFIED = "phone_number_verified",
  SECURE_PAGE_ACCESS = "custom:secure_page_access",
  FIRST_SIGN_IN = "custom:first_sign_in",
  GENDER = "gender",
  BIRTH_DATE = "birthdate",
  PICTURE = "picture",
  IDENTITIES = "identities",
}

export enum COGNITO_USER_TYPE {
  COGNITO = "cognito",
  GOOGLE = "google",
  FACEBOOK = "facebook",
  APPLE = "signinwithapple",
  LEGACY = "legacy",
}

export enum ASIDE_PAGES {
  EDIT_PROFILE = "profile",
  CHANGE_PASSWORD = "change-password",
  ORDERED = "ordered",
  RECIEVED = "recieved",
  REDEEMED = "redeemed",
  EXPIRED = "expired",
  SAVED_CARDS = "saved-cards",
}

export enum PROFILE_MENU {
  EDIT_PROFILE = "profile",
  DELETE_PROFILE = "delete",
}

export enum SIGN_IN_SLUGS {
  SIGN_IN = "login",
  EMAIL_LOGIN = "email-login",
  CHANGE_PASSWORD = "change-password",
  ADD_INFO = "add-info",
  VERIFY_OTP = "verify-otp",
  ADD_MOBILE = "add-mobile",
  MOBILE_LOGIN = "mobile",
  SECURE_PAGE = "secure-Page",
  VERIFY_EMAIL = "verify-email",
}

export enum PASSWORD_STRENGTH {
  GOOD = "good",
  BAD = "bad",
}
export enum FEEDBACK_STATUS {
  HAPPY = "happy",
  NOT_HAPPY = "not_happy",
  VERY_HAPPY = "very_happy",
}
export enum STATUS_CODE {
  STATUS_CODE_406 = 406,
  STATUS_CODE_403 = 403,
  STATUS_CODE_97200 = 97200,
  STATUS_CODE_97201 = 97201,
  STATUS_CODE_97401 = 97401,
  STATUS_CODE_97404 = 97404,
  STATUS_CODE_97405 = 97405,
}

export enum SIGN_UP_STATE {
  LEGACY_RESET_PASSWORD = "legacy-reset-password",
  NONE = "none",
}

export enum BRAND_REDEMPTION_TYPE {
  IN_STORE = "store",
  ONLINE = "online",
  ONLINE_AND_IN_STORE = "online_store",
}

// #. User Types
export enum USER_TYPES {
  LOGGED_IN = "Logged In",
  GUEST = "Guest",
}

export enum CAPTCHA_CONFIG_ACTION {
  DOWNLOAD_APP = "download_app",
  PRODUCT_FEEDBACK = "product_feedback",
  NEWS_LETTER = "newsletter",
  GIFT_ACCEPT_EMAIL = "gift_accept_email",
}

export enum CAPTCHA_CONFIG_ACTION {
  UPDATE_PHONE_NUMBER = "update_phone_number",
}

export enum FLOW_TYPE {
  CHANGE_PHONE_NUMBER = "ChangePhoneNumber",
}

export enum CHANNEL_TYPE {
  WHATSAPP = "WHATSAPP",
}

export enum DATE_FORMAT {
  DATE_DMY = "d MMM,  yyyy",
}

export enum SESSIONKEYS {
  PHONE_NUMBER = "phoneNumber",
}

export enum DIAL_CODES {
  UM = "1",
}

export enum COUNTRY_CODES {
  UM = "UM",
}

export enum FOOTER_ITEM_TYPE {
  ITEM_CODE_OUR_SOCIAL = "ITEM_CODE_OUR_SOCIAL",
  ITEM_CODE_OUR_SERVICES = "ITEM_CODE_OUR_SERVICES",
  ITEM_CODE_SUPPORT = "ITEM_CODE_SUPPORT",
  FB_ITEM_CODE = "FB_ITEM_CODE",
  ITEM_CODE_FOR_DEVELOPERS = "ITEM_CODE_FOR_DEVELOPERS",
}

export enum FOOTER_ALIGNMENT {
  VERTICAL = "VERTICAL",
  HORIZONTAL = "HORIZONTAL",
}

export enum OFFER_TYPE {
  PROMO_CODE = "promo_code",
  EXTRA_VALUE = "extra_value",
}

export enum TOAST_TYPE {
  CAPSULE = "capsule"
}

export enum MENU_TYPE{
  CAREER = "CR_ITEM_CODE"
}

export const GROUP_GIFT_APP_URL = "https://app.groupgift.yougotagift.com/";
export const BUSINESS_APP_URL = "https://yougotagift.com/business/";
export const BUSINESS_APP_URL_AR = "https://yougotagift.com/business/ar";
export const HELP_CENTER_URL = "https://support.yougotagift.com/hc/en-us";
export const HELP_CENTER_URL_AR = "https://support.yougotagift.com/hc/ar";
export const YGAG_BLOG_URL = "https://blog.yougotagift.com/";
export const PAGE_CACHE_CONFIG = "no-store";
export const YGAG_REGION_COOKIE = "YGAG_REGION_INFO";
export const YGAG_REGION_CODE_COOKIE = "YGAG_REGION_CODE";
export const GENERIC_BRAND_LISTS_PATH = "genericBrandLists";
export const GUEST_USER_SESSION_COOKIE = "session_ygag";
export const SNOW_ENABLED = "SNOW_ENABLED";
export const VOLUME_ENABLED = "VOLUME_ENABLED";
