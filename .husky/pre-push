#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo '🏗️👷 Styling, testing and building your project before committing'

# Check stylelint Standards
npm run stylelint ||
(
        echo '🤡😂❌🤡 Stylelint Check Failed. 🤡😂❌🤡
                 Make the required changes listed above, add changes and try to commit again.'
        false; 
)

# Check test Standards
npm run test ||
(
        echo '🤡😂❌🤡 Unit Testing Check Failed. 🤡😂❌🤡
                Make the required changes listed above, add changes and try to commit again.'
        false; 
)

# Check tsconfig standards
npm run check-types ||
(
    echo '🤡😂❌🤡 Failed Type check. 🤡😂❌🤡
            Are you seriously trying to write that? Make the changes required above.'
    false;
)

# Check ESLint Standards
# npm run check-lint ||
# (
#         cho '🤡😂❌🤡 ESLint Check Failed. 🤡😂❌🤡
#                  Make the required changes listed above, add changes and try to commit again.'
#         false; 
# )

# If everything passes... Now we can commit
#echo '🤔🤔🤔🤔... Alright... Code looks good to me... Trying to build now. 🤔🤔🤔🤔'

# npm run build ||
# (
#     echo '❌👷🔨❌ Did you do something wrong?... Because your build failed ❌👷🔨❌
#             Next build failed: View the errors above to see why.'
#     false;
# )

# If everything passes... Now we can commit
echo '✅✅✅✅ You win this time... I am committing this now. ✅✅✅✅'