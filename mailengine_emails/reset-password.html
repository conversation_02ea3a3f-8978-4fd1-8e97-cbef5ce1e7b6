
<!doctype html>

<html>
<head>
<!--[if gte mso 9]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
<meta http-equiv="Content-type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="format-detection" content="date=no" />
<meta name="format-detection" content="address=no" />
<meta name="format-detection" content="telephone=no" />
<meta name="x-apple-disable-message-reformatting" />
<!--[if !mso]><!-->
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
<!--<![endif]-->
<title>Login</title>
<!--[if gte mso 9]>
	<style type="text/css" media="all">
		sup { font-size: 100% !important; }
	</style>
	<![endif]-->

<style type="text/css" media="screen">
/* Linked Styles */
body {
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
    min-width: 100% !important;
    width: 100% !important;
    background: #001f51;
    -webkit-text-size-adjust: none
}
a {
    color: #000001;
    text-decoration: none
}
p {
    padding: 0 !important;
    margin: 0 !important
}
	.pointer {cursor: pointer;}
img {
    -ms-interpolation-mode: bicubic; /* Allow smoother rendering of resized image in Internet Explorer */
}
.mcnPreviewText {
    display: none !important;
}
.mainwrapper {
    background: #fff;
    width: 600px;
    margin: 10px 0px 30px 0px;
}
.tdwrap {
    width: 600px;
    min-width: 600px;
    font-size: 0pt;
    line-height: 0pt;
    margin: 0;
    font-weight: normal;
    padding: 40px 0px 0px 0px;
}
.user-name{
    font-family: Helvetica;
    font-size: 16px;
    font-weight: normal;
    line-height: 26px;
    
} 
.reset{
    font-weight: bold;
}
.login-Button{
  text-align: center;
  width: 253px;
  height: 50px;
  border-radius: 6px;
  border: solid 1px #b818c4 !important;
  background-color: #b818c4 !important;
  color: #fff !important;
  font-size: 16px;
  font-weight: bold;
  font-family: Helvetica;
  cursor: pointer;

}
.login-Button a{color: #fff !important;}
.pm {
    padding: 0px 0px 30px 0px;
    border-radius: 0px 0px 0px 0px;
}
.follow {
    color: #000;font-family: 'Roboto', sans-serif;
    font-size: 26px;
    line-height: 32px;
    text-align: center;
    padding-bottom: 20px;
    font-weight: bold;
}
.tflag {
    float: right !important;
}
.eGiftcards {
    color: #ffffff;font-family: 'Roboto', sans-serif;
    font-size: 18px;
	font-weight: bold;
    line-height: 38px;
    text-align: right !important;
    float: right !important;
}
.text-header {
    float: right !important;
    text-align: right !important;
}
.border {
    border-bottom: 1px solid #ececec !important;
}
.pbtm {
    padding-bottom: 30px !important;
}
.subtitle {
    color: #000000;font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: normal !important;
    line-height: 32px;
}
.pinkclr {
    color: #C82C91;
    font-weight: 600;
}
.fnt-bold {
    font-weight: 600;
    color: #000;
}
.blcont {
    color: #000000;font-family: 'Roboto', sans-serif;
    font-size: 26px;
    line-height: 32px;
    text-align: center !important;
    padding-bottom: 15px;
    padding-top: 15px;
}
.pbt30 {
    padding-bottom: 30px;
    border-bottom: 1px solid #e4e4e4;
}
.pinkbld {
    color: #C82C91;
    font-weight: bold !important;
}
.leftAlign_content {
    color: #000000;font-family: 'Roboto', sans-serif;
    font-size: 14px;
    line-height: 24px;
    text-align: left;
    padding-bottom: 0px;
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 30px;
}
.und {
    color: #000000;font-family: 'Roboto', sans-serif;
    font-size: 14px;
    line-height: 27px;
    padding-bottom: 15px;
    padding-top: 15px;
}
.yougg {
    font-size: 24px;
    font-weight: bold;
}
.colortitle {
    color:#00796B;
    font-weight: bold !important;
}
	
	
.MainText {
    line-height: 40px;
    font-size: 27px;
    font-weight: bold;
    color: #000000;font-family: 'Roboto', sans-serif;
    text-align: center !important;
    padding-bottom: 0px !important;
    padding-top: 5px !important;
	
}
	.TopText {
   
    font-size: 14px;
    font-weight: bold;
    color: #000000;font-family: 'Roboto', sans-serif;
    text-align: center !important;
    
    padding: 30px 40px 19px;
	
	
}
	.TopText2{margin: 13px 0 0;font-family: 'Roboto', sans-serif;
  font-size: 24px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #595959;}
	
	.Rounded-Button-S {
 width: 342px;
    height: 30px;font-family: 'Roboto', sans-serif;
    padding: 10px 30px;
    border-radius: 4px;
    background-color: #1c66af;
    color: #ffffff !important;
    text-align: center;
    font-size: 16px;
		line-height: 24px;
		
}
	.Rounded-Button-S a{color: #ffffff;}
	.Rounded-Button-S a:hover {color:#fef300;}
	
	
	.AmArWrapper{font-size:14px; color: #545454;font-family: 'Roboto', sans-serif; line-height:25.4px; text-align:center; padding-top: 7px; width:342px; height: 76px; border: 1px solid #1c66af; border-radius: 0px 0px 16px 16px; position: relative; z-index: 999; top: -20px; background: #ffffff;}
	.AmArea{ width: 342px; height: auto; margin: 0px auto;}
	.FOtter{width: 520px; height: auto;margin: 0px auto; }
	
	.btmfltitle{font-size: 12px; color: #000000;font-family: Arial;
    text-align: center !important; padding-top: 20px; line-height: 12px; padding-bottom: 20px;}
	
	.Topbrd{border-bottom: 6px solid #862882; width: 110px; float: none; margin: 0px auto; display: block; margin-bottom: 30px; }
	.fontN{ font-size: 28px !important; font-weight: 200 !important;}
	.footer{padding-bottom: 20px; padding-top: 18px; padding-left: 20px; padding-right: 20px;}
	.smedia{ float: left;}
	
.mainbanner {
    padding-bottom: 30px;
}
	.flagpad {
    padding-top: 22px !important;
}
	.middleblock{height:auto; padding-top:0px; padding-bottom: 30px; text-align: center;}
	.footer{padding-bottom: 20px; padding-top: 18px; padding-left: 20px; padding-right: 20px;}
	

/* Mobile styles */
@media only screen and (max-device-width: 480px), only screen and (max-width: 480px) {
.mainwrapper {
    background: #fff;
    width: 100% !important;
    margin: 0px !important;
    min-width: 100% !important;
}
.mobile-shell {
    width: 90% !important;
    min-width: 90% !important;
    border: none !important;
}
.bg {
    background-size: 100% auto !important;
    -webkit-background-size: 100% auto !important;
}
.helpline-country{
    font-size: 13px !important; 
}
.helpline-phone{
    font-size: 13px !important; 
}
.helpline-description{
    font-size: 10px !important;
}
.text-header, .m-center {
    text-align: center !important;
    float: none !important;
    padding-top: 10px;
}
.center {
    margin: 0 auto !important;
}
.container {
    padding: 20px 10px !important;
}
.td {
    width: 100% !important;
    min-width: 100% !important;
}
.m-br-15 {
    height: 15px !important;
}
.p30-15 {
    padding: 30px 15px !important;
}
.p0-15-30 {
    padding: 0px 15px 30px 15px !important;
}
.mpb30 {
    padding-bottom: 30px !important;
}
.pbt30 {
    padding-bottom: 0px !important;
    border-bottom: 1px solid #e4e4e4;
}
.m-td, .m-hide {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    font-size: 0 !important;
    line-height: 0 !important;
    min-height: 0 !important;
}
.m-block {
    display: block !important;
}
.fluid-img img {
   /* width: 100% !important;*/
    max-width: 100% !important;
    height: auto !important;
}
/*.blcont{text-align: left !important;}*/

.column, .column-dir, .column-top, .column-empty, .column-empty2, .column-dir-top {
    float: left !important;
    width: 100% !important;
    display: block !important;
}
.column-empty {
    padding-bottom: 30px !important;
}
/*.column-empty2 { padding-bottom: 10px !important; }*/

.content-spacing {
    width: 15px !important;
}
.brands {
    text-align: center !important;
    float: left !important;
    margin-left: 28px;
}
.mainwrapper {
    border: none !important;
}
.subtitle {
    text-align: left !important;
}
.maintitle {
    text-align: left !important;
}
.contentarea {
    text-align: left !important;
}
.buybtn {
    text-align: left !important;
}
.pull-left {
    text-align: left !important;
}
.text-footerlf {
    width: 100% !important;
    float: left !important;
    text-align: center !important;
}
.text-footerrt {
    width: 100% !important;
    float: right !important;
    text-align: center !important;
}
.divider {
    display: none !important;
}
.tdwrap {
    padding-top: 3px !important;
    padding-bottom: 3px !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.LoGo {
    width: 100px !important;
    height: 69px !important;
}
.pm {
    padding-bottom: 20px !important;
}
.follow {
    font-size: 20px !important;
    text-align: center !important;
}
.tflag {
    float: none !important;
    margin: 0px auto 0px !important;
    text-align: center !important;
}
.eGiftcards {
    float: none !important;
    margin: 0px auto 0px !important;
    text-align: center !important;
    line-height: 20px;
}
.subtitle {
    line-height: 26px !important;
    padding-top: 5px !important;
}
.plf0 {
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.leftAlign_content {
    padding-top: 15px !important;
    font-size: 14px !important;
}
.yougg {
    font-size: 16px !important;
    font-weight: bold !important;
}
.und {
    color: #000000;font-family: 'Roboto', sans-serif;
    font-size: 16px;
    line-height: 26px;
    text-align: center;
    padding-bottom: 0px;
    text-align: left;
    padding-top: 15px !important;
}
	.MainText {
    line-height: 24px !important;
    font-size: 20px !important;
    font-weight: bold;
    color: #000000;font-family: 'Roboto', sans-serif;
    text-align: center !important;
    padding-bottom: 0px !important;
    padding-top: 5px !important;
}
	
	.TopText {
    font-size: 14px;
    font-weight: bold;
    color: #000000;font-family: 'Roboto', sans-serif;
    text-align: center !important;
    
    padding: 30px 40px 19px;
	
}
	.TopText2{margin: 13px 0 0;font-family: 'Roboto', sans-serif;
  font-size: 18px !important;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #595959;}
	
	
	.mainbanner {
    padding-bottom: 20px !important;
}
	.flagpad {
    padding-top:0px !important;
}
	.GGM{margin: 0px auto !important;}
	.middleblock{width: 100%; float: left;}
	.smedia {
    padding-bottom: 10px !important;
    padding-top: 10px !important;
    margin: 0px auto !important;
    float: none !important;
    display: block !important;
}
	.imgmiddile {
    margin: 0px auto;
    text-align: center;
    float: none;
    display: block;
		padding-bottom: 20px;
}
	.text{text-align: center !important;}
	.mrauto{margin: 0px auto;}
	.fontN {
    font-size: 16px !important;
    font-weight: 200 !important;
}
	.white{text-align: center !important;}
	.footer{padding-bottom: 20px; padding-top: 18px; padding-left: 10px; padding-right: 10px;}
	
	.AmArea{ width:100%; height: auto; margin: 0px auto; float: none;}
	.FOtter{width: 100%; margin: 0px auto; float: none;}
	
	
}

@media only screen and (max-device-width: 359px), only screen and (max-width: 359px) {
.butnimg {
    width: 124px !important;
    height: 38px !important;
}
}
</style>
</head>
<body class="body" style="padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important; background:#fff; -webkit-text-size-adjust:none;">
<table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#fff">
  <tr>
    <td align="center" valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
          <td ><table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tr>
                
              </tr>
            </table></td>
        </tr>
      </table>
      <div class="mainwrapper " >
        <table width="800" cellspacing="0" cellpadding="0" class="mobile-shell"  style="border:1px solid;">
          <tr>
            <td class="td container tdwrap" ><!-- Header -->
              
              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td class="pm tbrr"><table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <th class="column-top" width="145" style="font-size:0pt; line-height:0pt; padding:0; margin:0; font-weight:normal; vertical-align:top;"> <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                              <td class="img m-center" style="font-size:0pt; line-height:0pt; text-align:center;width: 100%;"><img class="LoGo" src="./Images/ygag-logo.png" width="100" height="69" border="0" alt="eXtra" /></td>
                            </tr>
                          </table>
                        </th>
                        
                      </tr>
                    </table></td>
                </tr>
              </table>
              
             
              
              <table width="100%" border="0" cellspacing="0" cellpadding="0">
				  
				
				
                <tr>
                  <td  >
					
					<table align="center" cellspacing="0" cellpadding="0">
                        <tr>
                            <td style="font-size:24px; font-family: 'Roboto', sans-serif; font-weight: bold;line-height:36px; color: #000000; text-align:center; padding-top: 7px; padding-bottom: 20px; height: 150px; background: #f2f5f8; width: 600px; border-top-right-radius: 20px; border-top-left-radius: 20px;">
                                <table width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td style="font-size:26px; font-weight: bold; font-family: Helvetica;  color: #000; text-align: center; padding-bottom: 20px;">
                                            Password Reset Request
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="user-name">
                                            Hi Ali,
                                        </td>
                                    </tr>
                                    <tr>
                                      <td class="user-name">
                                          We have received a request to reset your password.
                                      </td>
                                  </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 600px; height: 245px; background-color: #fff; border: solid 1px #f2f5f8; border-top: none; border-bottom: none;">
                            <table align="center" cellspacing="0" cellpadding="0" width="100%">
                                <tr>
                                    <td style="font-size:0pt; line-height:0pt;width: 100%; text-align:center;">
                                        <img  src="./Images/lock.svg" width="87" height="100" border="0" style="object-fit: contain; margin-top: 20px;" alt="" />
                                    </td>
                                </tr>
                            </table>
                            <table align="center" style="margin-top: 23px;">
                                <tr>
                                    <td class="login-Button"><a href="#" target="_blank">Reset Password</a></td>
                                </tr>
                                <tr style="text-align:center">
                                  <td class="user-name" style="padding: 10px 0 20px;">This link will expire in 24 hours.</td>
                              </tr>
                            </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td style="background-color: #b818c4; height: 3px;"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>


                        <tr>
                            <td style="width: 600px; height: 85px; background-color: rgba(160, 62, 150, 0.05);">
                                <table align="center" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td style="background-color: #fff; border: solid 1px #fae0f7; height: 47px; width: 540px; text-align: center; font-size: 13px;  font-weight: normal; font-family: Helvetica;  line-height: 1.54;"> If you did not request a password change, please
                                          ignore this email.</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                          <td style="text-align: left; font-size: 25px;  font-weight: bold; font-family: Helvetica; padding-top: 30px; padding-bottom: 20px; line-height: normal;"> Need Help?</td>
                        </tr>
                        <tr>
                          <td
                            style="
                              font-size: 16px;
                              font-weight: normal;
                              font-family: Helvetica;
                              line-height: normal;
                              padding: 10px 0 40px;
                              border-bottom: 1px solid #e5e5e5;
                            "
                          >
                            Have any queries or doubts? Visit our
                            <a
                              href="https://support.yougotagift.com/hc/en-us/"
                              style="
                                color: #b800c4;
                                text-decoration: underline;
                              "
                              >Help Center</a
                            >
                          </td>
                          <td
                            style="
                              text-align: center;
                              font-size: 16px;
                              font-weight: normal;
                              font-family: Helvetica;
                              line-height: 0pt;
                              padding-bottom: 20px;
                            "
                          >
                            &nbsp;
                          </td>
                        </tr>
                                    <tr>
                                        <td style="height: 1px; background-color: #e5e5e5;"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <table align="center" width="100%" style="margin-top: 20px;">
                        <tr>
                            <td style=" text-align: center; font-weight: normal; font-size: 14px; font-family: Helvetica; padding-bottom: 65px; line-height: normal;"> <span style="unicode-bidi: plaintext;">&nbsp;© 2013 - 2022 YOUGotaGift.com Ltd. </span>  -  - <a href="#" style="color: #b800c4;">Privacy Policy </a> | <a href="#" style="color: #b800c4;">Terms of Use</a> </td>
                        </tr>
                    </table>
					</td>
                </tr>
              </table>
             </td>
          </tr>
        </table>
        &nbsp;&nbsp; </div></td>
  </tr>
</table>
</body>
</html>